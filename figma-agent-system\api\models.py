"""
Pydantic models for API request/response validation.
"""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timezone
from enum import Enum

from pydantic import BaseModel, Field, validator


class ExecutionStrategy(str, Enum):
    """Execution strategy for router requests."""
    SEQUENTIAL = "sequential"
    CONCURRENT = "concurrent"
    PIPELINE = "pipeline"
    BROADCAST = "broadcast"


class AgentRequestModel(BaseModel):
    """Model for agent task requests."""
    
    task_id: str = Field(..., description="Unique task identifier")
    action: str = Field(..., description="Action to perform")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Action parameters")
    context: Dict[str, Any] = Field(default_factory=dict, description="Execution context")
    priority: int = Field(default=5, ge=1, le=10, description="Task priority (1-10)")
    timeout: float = Field(default=30.0, gt=0, description="Task timeout in seconds")
    
    @validator('task_id')
    def validate_task_id(cls, v):
        if not v or not v.strip():
            raise ValueError('task_id cannot be empty')
        return v.strip()
    
    @validator('action')
    def validate_action(cls, v):
        if not v or not v.strip():
            raise ValueError('action cannot be empty')
        return v.strip()
    
    class Config:
        schema_extra = {
            "example": {
                "task_id": "task_123",
                "action": "create_frame",
                "parameters": {
                    "width": 800,
                    "height": 600,
                    "name": "Main Frame",
                    "background_color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}
                },
                "context": {
                    "file_key": "abc123",
                    "user_id": "user_456"
                },
                "priority": 7,
                "timeout": 15.0
            }
        }


class AgentResponseModel(BaseModel):
    """Model for agent task responses."""
    
    task_id: str = Field(..., description="Task identifier")
    agent_id: str = Field(..., description="Agent that processed the task")
    status: str = Field(..., description="Task execution status")
    result: Optional[Dict[str, Any]] = Field(None, description="Task execution result")
    error: Optional[str] = Field(None, description="Error message if failed")
    execution_time: float = Field(default=0.0, description="Execution time in seconds")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Response timestamp")
    
    class Config:
        schema_extra = {
            "example": {
                "task_id": "task_123",
                "agent_id": "design_agent",
                "status": "completed",
                "result": {
                    "frame_id": "frame_789",
                    "success": True
                },
                "execution_time": 2.5,
                "metadata": {
                    "tools_used": ["create_frame"],
                    "ai_assistance": False
                }
            }
        }


class RouterRequestModel(BaseModel):
    """Model for router requests."""
    
    request_id: str = Field(..., description="Unique request identifier")
    user_query: str = Field(..., description="Natural language query from user")
    context: Dict[str, Any] = Field(default_factory=dict, description="Request context")
    execution_strategy: ExecutionStrategy = Field(
        default=ExecutionStrategy.SEQUENTIAL,
        description="Execution strategy for tasks"
    )
    priority: int = Field(default=5, ge=1, le=10, description="Request priority")
    timeout: float = Field(default=60.0, gt=0, description="Overall timeout in seconds")
    
    @validator('request_id')
    def validate_request_id(cls, v):
        if not v or not v.strip():
            raise ValueError('request_id cannot be empty')
        return v.strip()
    
    @validator('user_query')
    def validate_user_query(cls, v):
        if not v or not v.strip():
            raise ValueError('user_query cannot be empty')
        if len(v.strip()) > 10000:
            raise ValueError('user_query too long (max 10000 characters)')
        return v.strip()
    
    class Config:
        schema_extra = {
            "example": {
                "request_id": "req_123",
                "user_query": "Create a responsive landing page with header, hero section, and footer",
                "context": {
                    "file_key": "abc123",
                    "user_id": "user_456",
                    "target_audience": "developers"
                },
                "execution_strategy": "sequential",
                "priority": 8,
                "timeout": 120.0
            }
        }


class RouterResponseModel(BaseModel):
    """Model for router responses."""
    
    request_id: str = Field(..., description="Request identifier")
    status: str = Field(..., description="Overall request status")
    tasks_created: List[Dict[str, Any]] = Field(default_factory=list, description="Created tasks")
    task_results: List[AgentResponseModel] = Field(default_factory=list, description="Task execution results")
    execution_plan: Dict[str, Any] = Field(default_factory=dict, description="Execution plan details")
    total_execution_time: float = Field(default=0.0, description="Total execution time")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")
    
    class Config:
        schema_extra = {
            "example": {
                "request_id": "req_123",
                "status": "completed",
                "tasks_created": [
                    {"task_id": "task_1", "agent": "design_agent", "action": "create_frame"},
                    {"task_id": "task_2", "agent": "text_agent", "action": "add_heading"}
                ],
                "task_results": [],
                "execution_plan": {
                    "strategy": "sequential",
                    "estimated_time": 45.0,
                    "complexity": 3
                },
                "total_execution_time": 42.3
            }
        }


class HealthResponse(BaseModel):
    """Health check response model."""
    
    status: str = Field(..., description="Service status")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Health check timestamp")
    version: str = Field(default="1.0.0", description="API version")
    uptime: float = Field(..., description="Service uptime in seconds")
    components: Dict[str, str] = Field(default_factory=dict, description="Component health status")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "healthy",
                "version": "1.0.0",
                "uptime": 3600.0,
                "components": {
                    "database": "healthy",
                    "gemini_client": "healthy",
                    "figma_api": "healthy",
                    "mcp_client": "healthy"
                }
            }
        }


class MetricsResponse(BaseModel):
    """Metrics response model."""
    
    total_requests: int = Field(..., description="Total requests processed")
    successful_requests: int = Field(..., description="Successful requests")
    failed_requests: int = Field(..., description="Failed requests")
    average_response_time: float = Field(..., description="Average response time in seconds")
    active_tasks: int = Field(..., description="Currently active tasks")
    agent_metrics: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict,
        description="Per-agent metrics"
    )
    system_metrics: Dict[str, Any] = Field(
        default_factory=dict,
        description="System-level metrics"
    )
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Metrics timestamp")
    
    class Config:
        schema_extra = {
            "example": {
                "total_requests": 1250,
                "successful_requests": 1180,
                "failed_requests": 70,
                "average_response_time": 2.3,
                "active_tasks": 5,
                "agent_metrics": {
                    "design_agent": {
                        "tasks_completed": 450,
                        "average_execution_time": 3.2,
                        "success_rate": 0.95
                    }
                },
                "system_metrics": {
                    "memory_usage": "512MB",
                    "cpu_usage": "15%",
                    "active_connections": 23
                }
            }
        }


class ErrorResponse(BaseModel):
    """Error response model."""
    
    error: str = Field(..., description="Error message")
    error_code: str = Field(..., description="Error code")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Error timestamp")
    request_id: Optional[str] = Field(None, description="Request ID if available")
    
    class Config:
        schema_extra = {
            "example": {
                "error": "Invalid request parameters",
                "error_code": "VALIDATION_ERROR",
                "details": {
                    "field": "width",
                    "message": "Width must be greater than 0"
                },
                "request_id": "req_123"
            }
        }


class TaskStatusModel(BaseModel):
    """Model for task status queries."""
    
    task_id: str = Field(..., description="Task identifier")
    status: str = Field(..., description="Current task status")
    progress: float = Field(default=0.0, ge=0.0, le=1.0, description="Task progress (0-1)")
    agent_id: Optional[str] = Field(None, description="Assigned agent ID")
    created_at: datetime = Field(..., description="Task creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")
    
    class Config:
        schema_extra = {
            "example": {
                "task_id": "task_123",
                "status": "in_progress",
                "progress": 0.65,
                "agent_id": "design_agent",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:32:30Z",
                "estimated_completion": "2024-01-15T10:35:00Z"
            }
        }
