"""
Basic integration tests for Figma agents.
Tests core functionality without complex dependencies.
"""

import pytest
import pytest_asyncio
import asyncio
import os
from unittest.mock import Mock, AsyncMock

# Set test environment variables before importing modules
os.environ["GEMINI_API_KEY"] = "test-key"
os.environ["FIGMA_ACCESS_TOKEN"] = "test-token"
os.environ["SECRET_KEY"] = "test-secret-key"

from agents.base_agent import BaseAgent, AgentRequest, AgentCapability, AgentStatus
from router.task_router import TaskRouter, RouterRequest, ExecutionStrategy


class MockAgent(BaseAgent):
    """Mock agent for testing."""

    def __init__(self, name: str, capabilities: list):
        super().__init__(name=name, capabilities=capabilities)
        self.tools = {}

    async def initialize(self):
        """Initialize mock agent."""
        self._status = AgentStatus.IDLE

    def get_supported_actions(self) -> list:
        """Get supported actions."""
        return ["test_action", "mock_action"]

    def _can_handle_action(self, action: str) -> bool:
        """Check if agent can handle action."""
        return action in self.get_supported_actions()

    async def _execute_action(self, action: str, parameters: dict, context: dict = None):
        """Execute specific action."""
        return {"mock": "result", "action": action}

    async def execute_task(self, request: AgentRequest):
        """Execute mock task."""
        from agents.base_agent import AgentResponse

        return AgentResponse(
            task_id=request.task_id,
            agent_name=self.name,
            status=AgentStatus.COMPLETED,
            result={"mock": "result"},
            execution_time=0.1
        )

    async def close(self):
        """Close mock agent."""
        self._status = AgentStatus.CANCELLED


class TestBasicIntegration:
    """Basic integration tests."""
    
    @pytest_asyncio.fixture
    async def mock_agents(self):
        """Create mock agents for testing."""
        agents = {
            'design': MockAgent('design_agent', [AgentCapability.DESIGN]),
            'component': MockAgent('component_agent', [AgentCapability.COMPONENT]),
            'text': MockAgent('text_agent', [AgentCapability.TEXT]),
            'color': MockAgent('color_agent', [AgentCapability.COLOR]),
            'image': MockAgent('image_agent', [AgentCapability.IMAGE]),
            'prototype': MockAgent('prototype_agent', [AgentCapability.PROTOTYPE]),
            'export': MockAgent('export_agent', [AgentCapability.EXPORT]),
            'collaboration': MockAgent('collaboration_agent', [AgentCapability.COLLABORATION])
        }
        
        for agent in agents.values():
            await agent.initialize()
        
        return agents
    
    @pytest_asyncio.fixture
    async def task_router(self, mock_agents):
        """Create task router with mock agents."""
        router = TaskRouter()
        
        for agent in mock_agents.values():
            router.register_agent(agent)
        
        return router
    
    @pytest.mark.asyncio
    async def test_agent_creation(self, mock_agents):
        """Test that all agents can be created."""
        assert len(mock_agents) == 8
        
        for agent_name, agent in mock_agents.items():
            assert agent.name == f"{agent_name}_agent"
            assert agent.is_available
            assert agent.status == AgentStatus.IDLE
    
    @pytest.mark.asyncio
    async def test_router_registration(self, task_router, mock_agents):
        """Test that all agents are registered with router."""
        assert len(task_router.agents) == 8
        
        # Test capability mapping
        for capability in AgentCapability:
            assert capability in task_router.capability_map
            assert len(task_router.capability_map[capability]) > 0
    
    @pytest.mark.asyncio
    async def test_basic_task_execution(self, mock_agents):
        """Test basic task execution."""
        test_request = AgentRequest(
            task_id="test_001",
            action="test_action",
            parameters={"test": "value"}
        )
        
        for agent in mock_agents.values():
            response = await agent.execute_task(test_request)
            
            assert response.task_id == test_request.task_id
            assert response.agent_name == agent.name
            assert response.status == AgentStatus.COMPLETED
    
    @pytest.mark.asyncio
    async def test_router_request_processing(self, task_router):
        """Test router request processing."""
        test_request = RouterRequest(
            request_id="router_001",
            user_query="Create a frame",
            execution_strategy=ExecutionStrategy.SEQUENTIAL
        )
        
        response = await task_router.process_request(test_request)
        
        assert response.request_id == test_request.request_id
        assert response.status in ["completed", "failed"]
        assert len(response.agent_responses) > 0
    
    @pytest.mark.asyncio
    async def test_concurrent_execution(self, task_router):
        """Test concurrent task execution."""
        test_request = RouterRequest(
            request_id="concurrent_001",
            user_query="Create frame and add text",
            execution_strategy=ExecutionStrategy.CONCURRENT
        )
        
        response = await task_router.process_request(test_request)
        
        assert response.request_id == test_request.request_id
        assert response.status in ["completed", "failed"]
    
    @pytest.mark.asyncio
    async def test_agent_capabilities(self, mock_agents):
        """Test that agents have correct capabilities."""
        expected_capabilities = {
            'design': AgentCapability.DESIGN,
            'component': AgentCapability.COMPONENT,
            'text': AgentCapability.TEXT,
            'color': AgentCapability.COLOR,
            'image': AgentCapability.IMAGE,
            'prototype': AgentCapability.PROTOTYPE,
            'export': AgentCapability.EXPORT,
            'collaboration': AgentCapability.COLLABORATION
        }
        
        for agent_name, expected_cap in expected_capabilities.items():
            agent = mock_agents[agent_name]
            assert expected_cap in agent.capabilities
    
    @pytest.mark.asyncio
    async def test_router_agent_selection(self, task_router):
        """Test that router selects appropriate agents."""
        test_queries = [
            ("Create a frame", AgentCapability.DESIGN),
            ("Add text", AgentCapability.TEXT),
            ("Apply color", AgentCapability.COLOR),
            ("Create component", AgentCapability.COMPONENT),
            ("Add image", AgentCapability.IMAGE),
            ("Create prototype", AgentCapability.PROTOTYPE),
            ("Export file", AgentCapability.EXPORT),
            ("Add comment", AgentCapability.COLLABORATION)
        ]
        
        for query, expected_capability in test_queries:
            request = RouterRequest(
                request_id=f"test_{query.replace(' ', '_')}",
                user_query=query,
                execution_strategy=ExecutionStrategy.SEQUENTIAL
            )
            
            # Analyze request to see if correct capability is detected
            task_plan = await task_router._analyze_request(request)
            
            # Check that at least one task uses the expected capability
            task_capabilities = [task.get("capability") for task in task_plan["tasks"]]
            assert expected_capability in task_capabilities or AgentCapability.DESIGN in task_capabilities


@pytest.mark.asyncio
async def test_system_startup_shutdown():
    """Test system startup and shutdown."""
    # Create mock agents
    agents = []
    for i in range(3):  # Just test with a few agents
        agent = MockAgent(f"test_agent_{i}", [AgentCapability.DESIGN])
        await agent.initialize()
        agents.append(agent)
    
    # Test that agents are initialized
    for agent in agents:
        assert agent.is_available
        assert agent.status == AgentStatus.IDLE
    
    # Test shutdown
    for agent in agents:
        await agent.close()
        assert agent.status == AgentStatus.CANCELLED


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
