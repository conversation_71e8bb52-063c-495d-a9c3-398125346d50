"""
Base Agent class for Figma Agent System.
Provides common functionality for all specialized agents following OpenAI SDK patterns.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field


class AgentStatus(str, Enum):
    """Agent execution status."""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AgentCapability(str, Enum):
    """Agent capabilities for task routing."""
    DESIGN = "design"
    COMPONENT = "component"
    TEXT = "text"
    COLOR = "color"
    IMAGE = "image"
    PROTOTYPE = "prototype"
    EXPORT = "export"
    COLLABORATION = "collaboration"


class AgentRequest(BaseModel):
    """Standard request format for all agents."""
    
    task_id: str = Field(..., description="Unique identifier for the task")
    action: str = Field(..., description="Specific action to perform")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Action parameters")
    context: Optional[Dict[str, Any]] = Field(default=None, description="Additional context")
    priority: int = Field(default=1, description="Task priority (1-10)")
    timeout: Optional[int] = Field(default=None, description="Task timeout in seconds")


class AgentResponse(BaseModel):
    """Standard response format for all agents."""
    
    task_id: str = Field(..., description="Task identifier")
    agent_name: str = Field(..., description="Name of the agent that processed the task")
    status: AgentStatus = Field(..., description="Execution status")
    result: Optional[Dict[str, Any]] = Field(default=None, description="Task result data")
    error: Optional[str] = Field(default=None, description="Error message if failed")
    execution_time: float = Field(..., description="Execution time in seconds")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class BaseAgent(ABC):
    """
    Abstract base class for all Figma agents.
    Implements common functionality and defines the interface for specialized agents.
    """
    
    def __init__(
        self,
        name: str,
        capabilities: List[AgentCapability],
        max_concurrent_tasks: int = 3,
        default_timeout: int = 60
    ):
        self.name = name
        self.capabilities = capabilities
        self.max_concurrent_tasks = max_concurrent_tasks
        self.default_timeout = default_timeout
        
        # Internal state
        self._status = AgentStatus.IDLE
        self._current_tasks: Dict[str, asyncio.Task] = {}
        self._task_history: List[AgentResponse] = []
        self._logger = logging.getLogger(f"agent.{name}")
        
        # Performance metrics
        self._total_tasks = 0
        self._successful_tasks = 0
        self._failed_tasks = 0
        self._average_execution_time = 0.0
        self._last_activity = None

        # Generate unique agent ID
        import uuid
        self.agent_id = str(uuid.uuid4())
    
    @property
    def status(self) -> AgentStatus:
        """Get current agent status."""
        return self._status
    
    @property
    def is_available(self) -> bool:
        """Check if agent can accept new tasks."""
        return (
            self._status in [AgentStatus.IDLE, AgentStatus.RUNNING] and
            len(self._current_tasks) < self.max_concurrent_tasks
        )
    
    @property
    def current_load(self) -> float:
        """Get current task load as percentage."""
        return len(self._current_tasks) / self.max_concurrent_tasks

    @property
    def total_tasks(self) -> int:
        """Get total number of tasks processed."""
        return self._total_tasks

    @property
    def successful_tasks(self) -> int:
        """Get number of successful tasks."""
        return self._successful_tasks

    @property
    def failed_tasks(self) -> int:
        """Get number of failed tasks."""
        return self._failed_tasks

    @property
    def average_execution_time(self) -> float:
        """Get average execution time."""
        return self._average_execution_time

    @property
    def last_activity(self) -> Optional[datetime]:
        """Get timestamp of last activity."""
        return self._last_activity
    
    @property
    def performance_metrics(self) -> Dict[str, Any]:
        """Get agent performance metrics."""
        success_rate = (
            self._successful_tasks / self._total_tasks
            if self._total_tasks > 0 else 0.0
        )
        
        return {
            "total_tasks": self._total_tasks,
            "successful_tasks": self._successful_tasks,
            "failed_tasks": self._failed_tasks,
            "success_rate": success_rate,
            "average_execution_time": self._average_execution_time,
            "current_load": self.current_load
        }
    
    async def execute_task(self, request: AgentRequest) -> AgentResponse:
        """
        Execute a task request.
        This is the main entry point for task execution.
        """
        start_time = asyncio.get_event_loop().time()
        
        # Validate request
        if not self._can_handle_action(request.action):
            return self._create_error_response(
                request.task_id,
                f"Agent {self.name} cannot handle action: {request.action}",
                start_time
            )
        
        # Check availability
        if not self.is_available:
            return self._create_error_response(
                request.task_id,
                f"Agent {self.name} is at maximum capacity",
                start_time
            )
        
        # Set timeout
        timeout = request.timeout or self.default_timeout
        
        try:
            # Update status
            if self._status == AgentStatus.IDLE:
                self._status = AgentStatus.RUNNING
            
            # Execute task with timeout
            task = asyncio.create_task(self._execute_action(request))
            self._current_tasks[request.task_id] = task
            
            result = await asyncio.wait_for(task, timeout=timeout)
            
            # Create success response
            execution_time = asyncio.get_event_loop().time() - start_time
            response = AgentResponse(
                task_id=request.task_id,
                agent_name=self.name,
                status=AgentStatus.COMPLETED,
                result=result,
                execution_time=execution_time
            )
            
            # Update metrics
            self._successful_tasks += 1
            self._update_average_execution_time(execution_time)
            
            self._logger.info(f"Task {request.task_id} completed successfully in {execution_time:.2f}s")
            
        except asyncio.TimeoutError:
            response = self._create_error_response(
                request.task_id,
                f"Task timed out after {timeout} seconds",
                start_time
            )
            self._failed_tasks += 1
            
        except Exception as e:
            response = self._create_error_response(
                request.task_id,
                f"Task failed: {str(e)}",
                start_time
            )
            self._failed_tasks += 1
            self._logger.error(f"Task {request.task_id} failed: {str(e)}")
            
        finally:
            # Cleanup
            self._current_tasks.pop(request.task_id, None)
            self._total_tasks += 1
            self._last_activity = datetime.utcnow()
            
            # Update status
            if len(self._current_tasks) == 0:
                self._status = AgentStatus.IDLE
            
            # Store in history
            self._task_history.append(response)
            
            # Limit history size
            if len(self._task_history) > 100:
                self._task_history = self._task_history[-100:]
        
        return response
    
    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a running task."""
        if task_id in self._current_tasks:
            task = self._current_tasks[task_id]
            task.cancel()
            self._current_tasks.pop(task_id, None)
            self._logger.info(f"Task {task_id} cancelled")
            return True
        return False
    
    def get_task_history(self, limit: int = 10) -> List[AgentResponse]:
        """Get recent task history."""
        return self._task_history[-limit:]
    
    @abstractmethod
    async def _execute_action(self, request: AgentRequest) -> Dict[str, Any]:
        """
        Execute the specific action requested.
        Must be implemented by specialized agents.
        """
        pass
    
    @abstractmethod
    def _can_handle_action(self, action: str) -> bool:
        """
        Check if this agent can handle the specified action.
        Must be implemented by specialized agents.
        """
        pass
    
    @abstractmethod
    def get_supported_actions(self) -> List[str]:
        """
        Get list of actions this agent supports.
        Must be implemented by specialized agents.
        """
        pass
    
    def _create_error_response(
        self,
        task_id: str,
        error_message: str,
        start_time: float
    ) -> AgentResponse:
        """Create an error response."""
        execution_time = asyncio.get_event_loop().time() - start_time
        return AgentResponse(
            task_id=task_id,
            agent_name=self.name,
            status=AgentStatus.FAILED,
            error=error_message,
            execution_time=execution_time
        )
    
    def _update_average_execution_time(self, execution_time: float) -> None:
        """Update the rolling average execution time."""
        if self._successful_tasks == 1:
            self._average_execution_time = execution_time
        else:
            # Simple moving average
            alpha = 0.1  # Weight for new measurement
            self._average_execution_time = (
                alpha * execution_time + 
                (1 - alpha) * self._average_execution_time
            )
    
    def __str__(self) -> str:
        return f"{self.name}Agent(status={self.status}, load={self.current_load:.1%})"
    
    def __repr__(self) -> str:
        return (
            f"{self.__class__.__name__}("
            f"name='{self.name}', "
            f"capabilities={self.capabilities}, "
            f"status={self.status})"
        )

    async def initialize(self) -> None:
        """Initialize the agent. Override in subclasses for specific initialization."""
        self._logger.info(f"Initializing {self.name}")
        self._status = AgentStatus.IDLE
        self._logger.info(f"{self.name} initialized successfully")

    def get_agent_metrics(self) -> Dict[str, Any]:
        """Get agent performance metrics."""
        success_rate = (
            self.successful_tasks / self.total_tasks
            if self.total_tasks > 0 else 0.0
        )

        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "status": self.status.value,
            "capabilities": [cap.value for cap in self.capabilities],
            "total_tasks": self.total_tasks,
            "successful_tasks": self.successful_tasks,
            "failed_tasks": self.failed_tasks,
            "success_rate": success_rate,
            "current_load": self.current_load,
            "max_concurrent_tasks": self.max_concurrent_tasks,
            "average_execution_time": self.average_execution_time,
            "last_activity": self.last_activity.isoformat() if self.last_activity else None
        }
