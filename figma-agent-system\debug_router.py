#!/usr/bin/env python3
"""
Debug script to test router functionality and identify issues.
"""

import sys
import os
import asyncio
import json
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Set test environment variables
os.environ["GEMINI_API_KEY"] = "test-key"
os.environ["FIGMA_ACCESS_TOKEN"] = "test-token"

async def test_router_request():
    """Test router request processing."""
    try:
        print("🔧 Testing router request processing...")

        from router.task_router import TaskRouter, RouterRequest, TaskPriority, ExecutionStrategy
        from agents.base_agent import AgentStatus
        from agents.design_agent import DesignAgent
        from agents.color_agent import ColorAgent
        from agents.component_agent import ComponentAgent

        # Create router
        router = TaskRouter()

        # Create and register agents
        design_agent = DesignAgent()
        color_agent = ColorAgent()
        component_agent = ComponentAgent()

        router.register_agent(design_agent)
        router.register_agent(color_agent)
        router.register_agent(component_agent)

        print(f"✅ Router created with {len(router.agents)} agents")
        
        # Create test request
        test_request = RouterRequest(
            request_id="debug-test-001",
            user_query="Create a blue button with rounded corners",
            context={
                "file_id": "test-file-123",
                "page_id": "test-page-456"
            },
            execution_strategy=ExecutionStrategy.SEQUENTIAL,
            priority=TaskPriority.NORMAL,
            timeout=30
        )
        
        print(f"✅ Test request created: {test_request.request_id}")
        print(f"   Query: {test_request.user_query}")
        print(f"   Context: {test_request.context}")
        
        # Process request
        print("🚀 Processing request...")
        response = await router.process_request(test_request)
        
        print(f"✅ Response received:")
        print(f"   Status: {response.status}")
        print(f"   Agent responses: {len(response.agent_responses)}")
        
        for i, agent_resp in enumerate(response.agent_responses):
            print(f"   Agent {i+1}:")
            print(f"     - Name: {agent_resp.agent_name}")
            print(f"     - Status: {agent_resp.status}")
            print(f"     - Error: {agent_resp.error}")
            print(f"     - Execution time: {agent_resp.execution_time}")
        
        return True
        
    except Exception as e:
        print(f"❌ Router test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_agent_direct():
    """Test agent execution directly."""
    try:
        print("\n🤖 Testing direct agent execution...")
        
        from agents.design_agent import DesignAgent
        from agents.base_agent import AgentRequest
        
        # Create agent
        agent = DesignAgent()
        print(f"✅ Agent created: {agent.name}")
        print(f"   Capabilities: {agent.capabilities}")
        print(f"   Supported actions: {agent.get_supported_actions()}")
        
        # Create test request
        test_request = AgentRequest(
            task_id="direct-test-001",
            action="create_frame",
            parameters={
                "width": 200,
                "height": 100,
                "name": "Test Button Frame"
            },
            context={
                "file_id": "test-file-123"
            }
        )
        
        print(f"✅ Test request created for action: {test_request.action}")
        
        # Execute task
        print("🚀 Executing task...")
        response = await agent.execute_task(test_request)
        
        print(f"✅ Response received:")
        print(f"   Task ID: {response.task_id}")
        print(f"   Agent: {response.agent_name}")
        print(f"   Status: {response.status}")
        print(f"   Result: {response.result}")
        print(f"   Error: {response.error}")
        print(f"   Execution time: {response.execution_time}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_models():
    """Test API model validation."""
    try:
        print("\n📋 Testing API model validation...")
        
        from api.models import RouterRequestModel, RouterResponseModel
        
        # Test valid request
        valid_request_data = {
            "request_id": "api-test-001",
            "user_query": "Create a simple button",
            "context": {"file_id": "test-file"},
            "execution_strategy": "sequential",
            "priority": 5,
            "timeout": 60.0
        }
        
        request_model = RouterRequestModel(**valid_request_data)
        print(f"✅ Valid request model created: {request_model.request_id}")
        
        # Test invalid request (missing required fields)
        try:
            invalid_request = RouterRequestModel(user_query="test")
            print("❌ Invalid request should have failed validation")
            return False
        except Exception as e:
            print(f"✅ Invalid request correctly rejected: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ API model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mcp_tools():
    """Test MCP tools integration."""
    try:
        print("\n🔧 Testing MCP tools integration...")
        
        from tools.mcp_integration import MCPClient, ToolRegistry, MCPTool
        
        # Create MCP client
        client = MCPClient()
        print(f"✅ MCP client created")
        
        # Create tool registry
        registry = ToolRegistry(client)
        print(f"✅ Tool registry created")
        
        # Create a test tool
        test_tool = MCPTool(
            name="test_figma_tool",
            description="Test Figma MCP tool",
            parameters={
                "file_id": {"type": str, "required": True, "description": "Figma file ID"}
            },
            handler=lambda params, context, client: {"success": True, "test": True},
            timeout=10.0,
            max_retries=1
        )
        
        # Register tool
        registry.register_tool(test_tool, "figma_test")
        print(f"✅ Test tool registered")
        
        # List tools
        tools = registry.list_tools()
        print(f"✅ Available tools: {len(tools)}")
        for tool_name in tools:
            print(f"   - {tool_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP tools test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all debug tests."""
    print("🚀 Starting Figma Agent System Debug Tests...")
    print("=" * 60)
    
    success = True
    
    # Test API models
    if not test_api_models():
        success = False
    
    # Test MCP tools
    if not test_mcp_tools():
        success = False
    
    # Test direct agent execution
    if not await test_agent_direct():
        success = False
    
    # Test router request processing
    if not await test_router_request():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All debug tests passed!")
    else:
        print("❌ Some debug tests failed. Check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
