"""
Figma MCP Tools Integration for Figma Agent System.
Integrates external Figma MCP tools with the agent system.
"""

import asyncio
import json
import os
import subprocess
import tempfile
import httpx
import websockets
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
from dataclasses import dataclass

from .mcp_integration import MCPClient, MCPTool, MCPResponse, ToolStatus, ToolRegistry
from config.settings import settings
from utils.logging_config import StructuredLogger


@dataclass
class FigmaMCPConfig:
    """Configuration for Figma MCP tools."""

    # MCP server configuration
    server_command: Optional[str] = None
    server_args: List[str] = None
    server_url: Optional[str] = None
    server_port: int = 3055

    # Tool-specific configuration
    figma_access_token: Optional[str] = None
    default_timeout: float = 30.0
    max_retries: int = 3

    # Channel configuration for Claude Talk to Figma
    default_channel: str = "oa34ym6m"  # Your active Figma plugin channel
    channel_per_agent: Dict[str, str] = None  # Optional: different channels per agent
    auto_join_channel: bool = True  # Automatically join channel on connection
    channel_timeout: float = 10.0  # Timeout for channel operations

    def __post_init__(self):
        if self.server_args is None:
            self.server_args = []

        # Use token from settings if not provided
        if not self.figma_access_token:
            self.figma_access_token = settings.figma.access_token

        # Set default server URL if not provided
        if not self.server_url:
            self.server_url = f"http://localhost:{self.server_port}"


class FigmaMCPClient:
    """
    Client for integrating with external Figma MCP tools.
    Handles communication with MCP servers and tool execution.
    """
    
    def __init__(self, config: FigmaMCPConfig = None):
        self.config = config or FigmaMCPConfig()
        self.logger = StructuredLogger("figma_mcp")
        
        # Tool registry for Figma-specific tools
        self.tool_registry = ToolRegistry()
        
        # Available Figma MCP tools
        self.available_tools = {
            "get_figma_data": {
                "description": "Get layout information about Figma file or specific nodes",
                "parameters": {
                    "fileKey": {"type": str, "required": True, "description": "Figma file key"},
                    "nodeId": {"type": str, "required": False, "description": "Specific node ID"},
                    "depth": {"type": int, "required": False, "description": "Traversal depth"}
                }
            },
            "download_figma_images": {
                "description": "Download SVG and PNG images from Figma",
                "parameters": {
                    "fileKey": {"type": str, "required": True, "description": "Figma file key"},
                    "nodes": {"type": list, "required": True, "description": "List of nodes to download"},
                    "localPath": {"type": str, "required": True, "description": "Local download path"},
                    "pngScale": {"type": float, "required": False, "description": "PNG scale factor"},
                    "svgOptions": {"type": dict, "required": False, "description": "SVG export options"}
                }
            },
            "figma_file_info": {
                "description": "Get basic information about a Figma file",
                "parameters": {
                    "fileKey": {"type": str, "required": True, "description": "Figma file key"}
                }
            },
            "figma_node_info": {
                "description": "Get detailed information about a specific node",
                "parameters": {
                    "fileKey": {"type": str, "required": True, "description": "Figma file key"},
                    "nodeId": {"type": str, "required": True, "description": "Node ID"}
                }
            }
        }
        
        self._register_figma_tools()
        self.logger.info("Figma MCP client initialized")
    
    def _register_figma_tools(self):
        """Register all available Figma MCP tools."""
        for tool_name, tool_info in self.available_tools.items():
            mcp_tool = MCPTool(
                name=tool_name,
                description=tool_info["description"],
                parameters=tool_info["parameters"],
                handler=self._create_tool_handler(tool_name),
                timeout=self.config.default_timeout,
                max_retries=self.config.max_retries
            )
            
            # Determine category based on tool name
            category = self._get_tool_category(tool_name)
            self.tool_registry.register_tool(mcp_tool, category)
    
    def _get_tool_category(self, tool_name: str) -> str:
        """Determine the category for a tool based on its name."""
        if "image" in tool_name or "download" in tool_name:
            return "image_operations"
        elif "data" in tool_name or "info" in tool_name:
            return "frame_management"
        elif "node" in tool_name:
            return "element_manipulation"
        else:
            return "frame_management"
    
    def _create_tool_handler(self, tool_name: str):
        """Create a handler function for a specific tool."""
        async def handler(parameters: Dict[str, Any], context: Dict[str, Any], client: MCPClient) -> Dict[str, Any]:
            return await self._execute_figma_mcp_tool(tool_name, parameters, context)
        return handler
    
    async def _execute_figma_mcp_tool(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Execute a Figma MCP tool using WebSocket communication with Claude Talk to Figma server.
        """
        try:
            self.logger.debug(f"Executing Figma MCP tool: {tool_name}", parameters=parameters)

            # Method 1: If you have a Claude Talk to Figma WebSocket server
            if self.config.server_url:
                return await self._execute_via_claude_talk_to_figma(tool_name, parameters, context)

            # Method 2: If you have a Figma MCP server command (subprocess)
            elif self.config.server_command:
                return await self._execute_via_mcp_server(tool_name, parameters, context)

            # Method 3: If you have direct tool functions
            else:
                return await self._execute_direct_tool(tool_name, parameters, context)

        except Exception as e:
            self.logger.error(f"Figma MCP tool execution failed: {tool_name}", error=str(e))
            raise

    async def _execute_via_claude_talk_to_figma(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute tool via Claude Talk to Figma WebSocket server."""
        try:
            # Convert HTTP URL to WebSocket URL
            ws_url = self.config.server_url.replace("http://", "ws://").replace("https://", "wss://")

            self.logger.debug(f"Connecting to Claude Talk to Figma WebSocket: {ws_url}")

            # Connect to WebSocket
            async with websockets.connect(ws_url) as websocket:
                # First, receive the system message
                system_message = await websocket.recv()
                system_data = json.loads(system_message)

                self.logger.debug(f"Received system message: {system_data}")

                # Join a channel (required by Claude Talk to Figma)
                channel_id = self._get_channel_for_context(context)
                join_message = {
                    "type": "join",
                    "channel": channel_id
                }

                await websocket.send(json.dumps(join_message))

                # Wait for join confirmation
                join_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                join_data = json.loads(join_response)

                self.logger.debug(f"Join response: {join_data}")

                # Now send the tool execution request
                tool_request = {
                    "type": "tool_call",
                    "tool": tool_name,
                    "parameters": parameters,
                    "id": f"{tool_name}_{asyncio.get_event_loop().time()}"
                }

                await websocket.send(json.dumps(tool_request))

                # Wait for tool response
                tool_response = await asyncio.wait_for(
                    websocket.recv(),
                    timeout=self.config.default_timeout
                )

                response_data = json.loads(tool_response)

                self.logger.debug(f"Tool response received: {response_data}")

                # Process the response
                if response_data.get("type") == "tool_result":
                    return response_data.get("result", {})
                elif response_data.get("type") == "error":
                    raise Exception(f"Tool execution error: {response_data.get('message', 'Unknown error')}")
                else:
                    # Return the raw response if format is unexpected
                    return response_data

        except asyncio.TimeoutError:
            raise Exception(f"Tool {tool_name} timed out after {self.config.default_timeout}s")
        except Exception as e:
            # Fallback to HTTP communication
            self.logger.warning(f"WebSocket communication failed, trying HTTP: {str(e)}")
            return await self._execute_via_http_server(tool_name, parameters, context)

    def _get_channel_for_context(self, context: Dict[str, Any] = None) -> str:
        """
        Determine which channel to use based on context and configuration.

        Args:
            context: Execution context that may contain channel preferences

        Returns:
            Channel ID to use for communication
        """
        # Priority order for channel selection:
        # 1. Explicit channel in context
        if context and "channel_id" in context:
            return context["channel_id"]

        # 2. Agent-specific channel if configured
        if context and "agent_name" in context:
            agent_name = context["agent_name"]
            if (self.config.channel_per_agent and
                agent_name in self.config.channel_per_agent):
                return self.config.channel_per_agent[agent_name]

        # 3. Default channel from configuration
        return self.config.default_channel

    def _get_channel_for_agent(self, agent_name: str) -> str:
        """
        Get the channel ID for a specific agent.

        Args:
            agent_name: Name of the agent (e.g., 'design_agent', 'color_agent')

        Returns:
            Channel ID for the agent
        """
        if (self.config.channel_per_agent and
            agent_name in self.config.channel_per_agent):
            return self.config.channel_per_agent[agent_name]

        return self.config.default_channel

    async def test_channel_connection(self, channel_id: str = None) -> Dict[str, Any]:
        """
        Test connection to a specific channel.

        Args:
            channel_id: Channel to test (uses default if None)

        Returns:
            Connection test results
        """
        if not channel_id:
            channel_id = self.config.default_channel

        try:
            ws_url = self.config.server_url.replace("http://", "ws://").replace("https://", "wss://")

            async with websockets.connect(ws_url) as websocket:
                # Receive system message
                system_message = await asyncio.wait_for(
                    websocket.recv(),
                    timeout=self.config.channel_timeout
                )
                system_data = json.loads(system_message)

                # Join the channel
                join_message = {"type": "join", "channel": channel_id}
                await websocket.send(json.dumps(join_message))

                # Wait for join confirmation
                join_response = await asyncio.wait_for(
                    websocket.recv(),
                    timeout=self.config.channel_timeout
                )
                join_data = json.loads(join_response)

                return {
                    "success": True,
                    "channel": channel_id,
                    "system_message": system_data,
                    "join_response": join_data,
                    "connection_time": asyncio.get_event_loop().time()
                }

        except Exception as e:
            return {
                "success": False,
                "channel": channel_id,
                "error": str(e),
                "connection_time": asyncio.get_event_loop().time()
            }

    async def _execute_via_http_server(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute tool via HTTP communication with MCP server."""
        try:
            # Prepare MCP request
            mcp_request = {
                "jsonrpc": "2.0",
                "id": f"{tool_name}_{asyncio.get_event_loop().time()}",
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": parameters
                }
            }

            self.logger.debug(f"Sending HTTP request to MCP server",
                            url=self.config.server_url,
                            request=mcp_request)

            # Send HTTP request to MCP server
            async with httpx.AsyncClient(timeout=self.config.default_timeout) as client:
                # Try different endpoints that your MCP server might use
                endpoints_to_try = [
                    f"{self.config.server_url}/tools/{tool_name}",
                    f"{self.config.server_url}/api/tools/{tool_name}",
                    f"{self.config.server_url}/mcp",
                    f"{self.config.server_url}/rpc"
                ]

                last_error = None

                for endpoint in endpoints_to_try:
                    try:
                        response = await client.post(
                            endpoint,
                            json=mcp_request,
                            headers={
                                "Content-Type": "application/json",
                                "Authorization": f"Bearer {self.config.figma_access_token}" if self.config.figma_access_token else None
                            }
                        )

                        if response.status_code == 200:
                            break

                    except Exception as e:
                        last_error = e
                        continue
                else:
                    # If all endpoints failed, raise the last error
                    if last_error:
                        raise last_error
                    else:
                        raise Exception(f"All endpoints failed for tool {tool_name}")

                if response.status_code != 200:
                    raise Exception(f"HTTP error {response.status_code}: {response.text}")

                # Parse response
                response_data = response.json()

                if "error" in response_data:
                    raise Exception(f"MCP tool error: {response_data['error']}")

                result = response_data.get("result", {})
                self.logger.debug(f"Received HTTP response from MCP server", result=result)

                return result

        except httpx.TimeoutException:
            raise Exception(f"Tool {tool_name} timed out after {self.config.default_timeout}s")
        except Exception as e:
            # Try WebSocket communication as fallback
            self.logger.warning(f"HTTP communication failed, trying WebSocket: {str(e)}")
            return await self._execute_via_websocket(tool_name, parameters, context)

    async def _execute_via_websocket(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute tool via WebSocket communication with MCP server."""
        try:
            # Convert HTTP URL to WebSocket URL
            ws_url = self.config.server_url.replace("http://", "ws://").replace("https://", "wss://")
            if not ws_url.endswith("/ws"):
                ws_url += "/ws"

            # Prepare MCP request
            mcp_request = {
                "jsonrpc": "2.0",
                "id": f"{tool_name}_{asyncio.get_event_loop().time()}",
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": parameters
                }
            }

            self.logger.debug(f"Connecting to WebSocket MCP server", url=ws_url)

            # Connect to WebSocket and send request
            async with websockets.connect(ws_url) as websocket:
                # Send request
                await websocket.send(json.dumps(mcp_request))

                # Receive response
                response_text = await asyncio.wait_for(
                    websocket.recv(),
                    timeout=self.config.default_timeout
                )

                response_data = json.loads(response_text)

                if "error" in response_data:
                    raise Exception(f"MCP tool error: {response_data['error']}")

                result = response_data.get("result", {})
                self.logger.debug(f"Received WebSocket response from MCP server", result=result)

                return result

        except Exception as e:
            raise Exception(f"WebSocket communication failed for {tool_name}: {str(e)}")
    
    async def _execute_via_mcp_server(
        self, 
        tool_name: str, 
        parameters: Dict[str, Any], 
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute tool via MCP server subprocess."""
        try:
            # Prepare MCP request
            mcp_request = {
                "jsonrpc": "2.0",
                "id": f"{tool_name}_{asyncio.get_event_loop().time()}",
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": parameters
                }
            }
            
            # Create temporary file for request
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(mcp_request, f)
                request_file = f.name
            
            try:
                # Execute MCP server command
                cmd = [self.config.server_command] + self.config.server_args + [request_file]
                
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    env={**dict(os.environ), "FIGMA_ACCESS_TOKEN": self.config.figma_access_token}
                )
                
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=self.config.default_timeout
                )
                
                if process.returncode != 0:
                    raise Exception(f"MCP server error: {stderr.decode()}")
                
                # Parse response
                response = json.loads(stdout.decode())
                
                if "error" in response:
                    raise Exception(f"MCP tool error: {response['error']}")
                
                return response.get("result", {})
                
            finally:
                # Clean up temporary file
                Path(request_file).unlink(missing_ok=True)
                
        except asyncio.TimeoutError:
            raise Exception(f"Tool {tool_name} timed out after {self.config.default_timeout}s")
        except Exception as e:
            raise Exception(f"Failed to execute {tool_name}: {str(e)}")
    
    async def _execute_direct_tool(
        self, 
        tool_name: str, 
        parameters: Dict[str, Any], 
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Execute tool directly (placeholder for your specific implementation).
        Replace this with your actual Figma MCP tool integration.
        """
        # This is a placeholder implementation
        # Replace with your actual Figma MCP tool calls
        
        if tool_name == "get_figma_data":
            return await self._mock_get_figma_data(parameters)
        elif tool_name == "download_figma_images":
            return await self._mock_download_images(parameters)
        elif tool_name == "figma_file_info":
            return await self._mock_file_info(parameters)
        elif tool_name == "figma_node_info":
            return await self._mock_node_info(parameters)
        else:
            raise Exception(f"Unknown tool: {tool_name}")
    
    # Mock implementations (replace with your actual tool integrations)
    async def _mock_get_figma_data(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Mock implementation for get_figma_data tool."""
        file_key = parameters.get("fileKey")
        node_id = parameters.get("nodeId")
        
        return {
            "success": True,
            "fileKey": file_key,
            "nodeId": node_id,
            "data": {
                "name": "Mock Figma File",
                "type": "DOCUMENT" if not node_id else "FRAME",
                "children": [] if node_id else [{"id": "mock-node", "name": "Mock Node"}]
            }
        }
    
    async def _mock_download_images(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Mock implementation for download_figma_images tool."""
        return {
            "success": True,
            "downloaded": len(parameters.get("nodes", [])),
            "path": parameters.get("localPath", "/tmp")
        }
    
    async def _mock_file_info(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Mock implementation for figma_file_info tool."""
        return {
            "success": True,
            "fileKey": parameters.get("fileKey"),
            "name": "Mock Figma File",
            "lastModified": "2025-06-29T12:00:00Z"
        }
    
    async def _mock_node_info(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Mock implementation for figma_node_info tool."""
        return {
            "success": True,
            "nodeId": parameters.get("nodeId"),
            "name": "Mock Node",
            "type": "FRAME",
            "absoluteBoundingBox": {"x": 0, "y": 0, "width": 100, "height": 100}
        }
    
    async def execute_tool(
        self, 
        tool_name: str, 
        parameters: Dict[str, Any], 
        context: Dict[str, Any] = None
    ) -> MCPResponse:
        """Execute a Figma MCP tool."""
        return await self.tool_registry.execute_tool(tool_name, parameters, context)
    
    def list_available_tools(self) -> List[str]:
        """List all available Figma MCP tools."""
        return list(self.available_tools.keys())
    
    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific tool."""
        return self.tool_registry.get_tool_info(tool_name)
    
    async def close(self):
        """Close the Figma MCP client."""
        await self.tool_registry.close()
        self.logger.info("Figma MCP client closed")


# Global instance for easy access
figma_mcp_client = None

def get_figma_mcp_client(config: FigmaMCPConfig = None) -> FigmaMCPClient:
    """
    Factory function to create a Figma MCP client with the specified configuration.

    Args:
        config: Configuration for the Figma MCP client. If None, uses Claude Talk to Figma configuration.

    Returns:
        Configured FigmaMCPClient instance
    """
    if config is None:
        # Use Claude Talk to Figma configuration by default
        config = FigmaMCPConfig(
            server_url="http://localhost:3055",
            server_port=3055,
            default_timeout=45.0,
            max_retries=2,
            default_channel="oa34ym6m",  # Your active Figma plugin channel
            auto_join_channel=True,
            channel_timeout=10.0
        )

    return FigmaMCPClient(config)

def get_claude_talk_to_figma_client(channel_id: str = "oa34ym6m",
                                   channel_per_agent: Dict[str, str] = None) -> FigmaMCPClient:
    """
    Factory function to create a Figma MCP client specifically configured for Claude Talk to Figma.

    Args:
        channel_id: Default channel ID to use (your active Figma plugin channel)
        channel_per_agent: Optional mapping of agent names to specific channels

    Returns:
        FigmaMCPClient configured for Claude Talk to Figma MCP server
    """
    config = FigmaMCPConfig(
        server_url="http://localhost:3055",
        server_port=3055,
        default_timeout=45.0,
        max_retries=2,
        default_channel=channel_id,
        channel_per_agent=channel_per_agent or {},
        auto_join_channel=True,
        channel_timeout=10.0
    )

    return FigmaMCPClient(config)
