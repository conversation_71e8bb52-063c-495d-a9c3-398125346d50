"""
Figma MCP Tools Integration for Figma Agent System.
Integrates external Figma MCP tools with the agent system.
"""

import asyncio
import json
import os
import subprocess
import tempfile
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
from dataclasses import dataclass

from .mcp_integration import MCPClient, MCPTool, MCPResponse, ToolStatus, ToolRegistry
from config.settings import settings
from utils.logging_config import StructuredLogger


@dataclass
class FigmaMCPConfig:
    """Configuration for Figma MCP tools."""
    
    # MCP server configuration
    server_command: Optional[str] = None
    server_args: List[str] = None
    
    # Tool-specific configuration
    figma_access_token: Optional[str] = None
    default_timeout: float = 30.0
    max_retries: int = 3
    
    def __post_init__(self):
        if self.server_args is None:
            self.server_args = []
        
        # Use token from settings if not provided
        if not self.figma_access_token:
            self.figma_access_token = settings.figma.access_token


class FigmaMCPClient:
    """
    Client for integrating with external Figma MCP tools.
    Handles communication with MCP servers and tool execution.
    """
    
    def __init__(self, config: FigmaMCPConfig = None):
        self.config = config or FigmaMCPConfig()
        self.logger = StructuredLogger("figma_mcp")
        
        # Tool registry for Figma-specific tools
        self.tool_registry = ToolRegistry()
        
        # Available Figma MCP tools
        self.available_tools = {
            "get_figma_data": {
                "description": "Get layout information about Figma file or specific nodes",
                "parameters": {
                    "fileKey": {"type": str, "required": True, "description": "Figma file key"},
                    "nodeId": {"type": str, "required": False, "description": "Specific node ID"},
                    "depth": {"type": int, "required": False, "description": "Traversal depth"}
                }
            },
            "download_figma_images": {
                "description": "Download SVG and PNG images from Figma",
                "parameters": {
                    "fileKey": {"type": str, "required": True, "description": "Figma file key"},
                    "nodes": {"type": list, "required": True, "description": "List of nodes to download"},
                    "localPath": {"type": str, "required": True, "description": "Local download path"},
                    "pngScale": {"type": float, "required": False, "description": "PNG scale factor"},
                    "svgOptions": {"type": dict, "required": False, "description": "SVG export options"}
                }
            },
            "figma_file_info": {
                "description": "Get basic information about a Figma file",
                "parameters": {
                    "fileKey": {"type": str, "required": True, "description": "Figma file key"}
                }
            },
            "figma_node_info": {
                "description": "Get detailed information about a specific node",
                "parameters": {
                    "fileKey": {"type": str, "required": True, "description": "Figma file key"},
                    "nodeId": {"type": str, "required": True, "description": "Node ID"}
                }
            }
        }
        
        self._register_figma_tools()
        self.logger.info("Figma MCP client initialized")
    
    def _register_figma_tools(self):
        """Register all available Figma MCP tools."""
        for tool_name, tool_info in self.available_tools.items():
            mcp_tool = MCPTool(
                name=tool_name,
                description=tool_info["description"],
                parameters=tool_info["parameters"],
                handler=self._create_tool_handler(tool_name),
                timeout=self.config.default_timeout,
                max_retries=self.config.max_retries
            )
            
            # Determine category based on tool name
            category = self._get_tool_category(tool_name)
            self.tool_registry.register_tool(mcp_tool, category)
    
    def _get_tool_category(self, tool_name: str) -> str:
        """Determine the category for a tool based on its name."""
        if "image" in tool_name or "download" in tool_name:
            return "image_operations"
        elif "data" in tool_name or "info" in tool_name:
            return "frame_management"
        elif "node" in tool_name:
            return "element_manipulation"
        else:
            return "frame_management"
    
    def _create_tool_handler(self, tool_name: str):
        """Create a handler function for a specific tool."""
        async def handler(parameters: Dict[str, Any], context: Dict[str, Any], client: MCPClient) -> Dict[str, Any]:
            return await self._execute_figma_mcp_tool(tool_name, parameters, context)
        return handler
    
    async def _execute_figma_mcp_tool(
        self, 
        tool_name: str, 
        parameters: Dict[str, Any], 
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Execute a Figma MCP tool using subprocess or direct API call.
        This method should be customized based on how your Figma MCP tools are implemented.
        """
        try:
            self.logger.debug(f"Executing Figma MCP tool: {tool_name}", parameters=parameters)
            
            # Method 1: If you have a Figma MCP server running
            if self.config.server_command:
                return await self._execute_via_mcp_server(tool_name, parameters, context)
            
            # Method 2: If you have direct tool functions
            else:
                return await self._execute_direct_tool(tool_name, parameters, context)
                
        except Exception as e:
            self.logger.error(f"Figma MCP tool execution failed: {tool_name}", error=str(e))
            raise
    
    async def _execute_via_mcp_server(
        self, 
        tool_name: str, 
        parameters: Dict[str, Any], 
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute tool via MCP server subprocess."""
        try:
            # Prepare MCP request
            mcp_request = {
                "jsonrpc": "2.0",
                "id": f"{tool_name}_{asyncio.get_event_loop().time()}",
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": parameters
                }
            }
            
            # Create temporary file for request
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(mcp_request, f)
                request_file = f.name
            
            try:
                # Execute MCP server command
                cmd = [self.config.server_command] + self.config.server_args + [request_file]
                
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    env={**dict(os.environ), "FIGMA_ACCESS_TOKEN": self.config.figma_access_token}
                )
                
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=self.config.default_timeout
                )
                
                if process.returncode != 0:
                    raise Exception(f"MCP server error: {stderr.decode()}")
                
                # Parse response
                response = json.loads(stdout.decode())
                
                if "error" in response:
                    raise Exception(f"MCP tool error: {response['error']}")
                
                return response.get("result", {})
                
            finally:
                # Clean up temporary file
                Path(request_file).unlink(missing_ok=True)
                
        except asyncio.TimeoutError:
            raise Exception(f"Tool {tool_name} timed out after {self.config.default_timeout}s")
        except Exception as e:
            raise Exception(f"Failed to execute {tool_name}: {str(e)}")
    
    async def _execute_direct_tool(
        self, 
        tool_name: str, 
        parameters: Dict[str, Any], 
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Execute tool directly (placeholder for your specific implementation).
        Replace this with your actual Figma MCP tool integration.
        """
        # This is a placeholder implementation
        # Replace with your actual Figma MCP tool calls
        
        if tool_name == "get_figma_data":
            return await self._mock_get_figma_data(parameters)
        elif tool_name == "download_figma_images":
            return await self._mock_download_images(parameters)
        elif tool_name == "figma_file_info":
            return await self._mock_file_info(parameters)
        elif tool_name == "figma_node_info":
            return await self._mock_node_info(parameters)
        else:
            raise Exception(f"Unknown tool: {tool_name}")
    
    # Mock implementations (replace with your actual tool integrations)
    async def _mock_get_figma_data(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Mock implementation for get_figma_data tool."""
        file_key = parameters.get("fileKey")
        node_id = parameters.get("nodeId")
        
        return {
            "success": True,
            "fileKey": file_key,
            "nodeId": node_id,
            "data": {
                "name": "Mock Figma File",
                "type": "DOCUMENT" if not node_id else "FRAME",
                "children": [] if node_id else [{"id": "mock-node", "name": "Mock Node"}]
            }
        }
    
    async def _mock_download_images(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Mock implementation for download_figma_images tool."""
        return {
            "success": True,
            "downloaded": len(parameters.get("nodes", [])),
            "path": parameters.get("localPath", "/tmp")
        }
    
    async def _mock_file_info(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Mock implementation for figma_file_info tool."""
        return {
            "success": True,
            "fileKey": parameters.get("fileKey"),
            "name": "Mock Figma File",
            "lastModified": "2025-06-29T12:00:00Z"
        }
    
    async def _mock_node_info(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Mock implementation for figma_node_info tool."""
        return {
            "success": True,
            "nodeId": parameters.get("nodeId"),
            "name": "Mock Node",
            "type": "FRAME",
            "absoluteBoundingBox": {"x": 0, "y": 0, "width": 100, "height": 100}
        }
    
    async def execute_tool(
        self, 
        tool_name: str, 
        parameters: Dict[str, Any], 
        context: Dict[str, Any] = None
    ) -> MCPResponse:
        """Execute a Figma MCP tool."""
        return await self.tool_registry.execute_tool(tool_name, parameters, context)
    
    def list_available_tools(self) -> List[str]:
        """List all available Figma MCP tools."""
        return list(self.available_tools.keys())
    
    def get_tool_info(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific tool."""
        return self.tool_registry.get_tool_info(tool_name)
    
    async def close(self):
        """Close the Figma MCP client."""
        await self.tool_registry.close()
        self.logger.info("Figma MCP client closed")


# Global instance for easy access
figma_mcp_client = None

def get_figma_mcp_client(config: FigmaMCPConfig = None) -> FigmaMCPClient:
    """Get or create the global Figma MCP client instance."""
    global figma_mcp_client
    if figma_mcp_client is None:
        figma_mcp_client = FigmaMCPClient(config)
    return figma_mcp_client
