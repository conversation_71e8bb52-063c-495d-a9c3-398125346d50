"""
Test suite for FastAPI endpoints.

Tests all REST API endpoints for the Figma Agent System.
"""

import pytest
import os
from unittest.mock import patch
from fastapi.testclient import TestClient

# Set test environment variables
os.environ["GEMINI_API_KEY"] = "test-key"
os.environ["FIGMA_ACCESS_TOKEN"] = "test-token"

from api.fastapi_server import app
from router.task_router import RouterResponse


class TestAPIEndpoints:
    """Test suite for API endpoints."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    def test_health_check(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data
    
    def test_system_metrics(self, client):
        """Test system metrics endpoint."""
        response = client.get("/metrics")
        assert response.status_code == 200
        data = response.json()
        assert "agents" in data
        assert "system" in data
        assert "timestamp" in data

    def test_design_agent_execute(self, client):
        """Test design agent execution endpoint."""
        request_data = {
            "task_id": "test-123",
            "action": "create_frame",
            "parameters": {"width": 800, "height": 600},
            "context": {"figma_file_id": "test-file"}
        }

        # This will fail without proper mocking, but tests the endpoint exists
        response = client.post("/agents/design/execute", json=request_data)
        # Should not be 404 (endpoint exists)
        assert response.status_code != 404

    def test_component_agent_execute(self, client):
        """Test component agent execution endpoint."""
        request_data = {
            "task_id": "test-456",
            "action": "create_component",
            "parameters": {"name": "Button"},
            "context": {"figma_file_id": "test-file"}
        }

        response = client.post("/agents/component/execute", json=request_data)
        # Should not be 404 (endpoint exists)
        assert response.status_code != 404

    def test_text_agent_execute(self, client):
        """Test text agent execution endpoint."""
        request_data = {
            "task_id": "test-789",
            "action": "create_text",
            "parameters": {"content": "Hello World"},
            "context": {"figma_file_id": "test-file"}
        }

        response = client.post("/agents/text/execute", json=request_data)
        # Should not be 404 (endpoint exists)
        assert response.status_code != 404
    
    def test_get_nonexistent_agent(self, client):
        """Test get details for non-existent agent."""
        response = client.get("/agents/nonexistent_agent")
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"].lower()
    
    @patch('router.task_router.TaskRouter.process_request')
    def test_router_process_success(self, mock_process, client):
        """Test successful router request processing."""
        # Mock successful response
        mock_response = RouterResponse(
            request_id="test-123",
            status="completed",
            result={"frame_id": "123:456", "success": True},
            agent_used="design_agent",
            execution_time=1.5,
            metadata={"action": "create_frame"}
        )
        mock_process.return_value = mock_response

        request_data = {
            "request_id": "test-123",
            "user_query": "Create a new frame",
            "figma_file_id": "test-file-123",
            "parameters": {"width": 800, "height": 600}
        }

        response = client.post("/router/process", json=request_data)
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"
        assert data["result"]["success"] is True
        assert data["agent_used"] == "design_agent"
    
    @patch('router.task_router.TaskRouter.process_request')
    def test_router_process_failure(self, mock_process, client):
        """Test failed router request processing."""
        # Mock failed response
        mock_response = RouterResponse(
            request_id="test-456",
            status="failed",
            error="Invalid parameters",
            agent_used=None,
            execution_time=0.5
        )
        mock_process.return_value = mock_response

        request_data = {
            "request_id": "test-456",
            "user_query": "Invalid request",
            "figma_file_id": "test-file-456"
        }

        response = client.post("/router/process", json=request_data)
        assert response.status_code == 400
        data = response.json()
        assert data["status"] == "failed"
        assert "Invalid parameters" in data["error"]

    def test_router_invalid_request(self, client):
        """Test router with invalid request data."""
        request_data = {
            "figma_file_id": "test-file-789"
            # Missing required user_query and request_id
        }

        response = client.post("/router/process", json=request_data)
        assert response.status_code == 422  # Validation error
    
    def test_task_status_endpoint_exists(self, client):
        """Test that task status endpoint exists."""
        # Test with a dummy task ID - should return 404 but not 405 (method not allowed)
        response = client.get("/tasks/dummy-task-id/status")
        # Should be 404 (not found) or 500 (server error), but not 405 (method not allowed)
        assert response.status_code in [404, 500]
        assert response.status_code != 405
    
    def test_endpoint_validation(self, client):
        """Test endpoint validation and error handling."""
        # Test design agent with invalid data
        response = client.post("/agents/design/execute", json={})
        assert response.status_code == 422  # Validation error

        # Test component agent with invalid data
        response = client.post("/agents/component/execute", json={})
        assert response.status_code == 422  # Validation error

        # Test text agent with invalid data
        response = client.post("/agents/text/execute", json={})
        assert response.status_code == 422  # Validation error
    
    def test_api_documentation(self, client):
        """Test API documentation endpoints."""
        # Test OpenAPI schema
        response = client.get("/openapi.json")
        assert response.status_code == 200
        schema = response.json()
        assert "openapi" in schema
        assert "paths" in schema

        # Test Swagger UI (should redirect or return HTML)
        response = client.get("/docs")
        assert response.status_code == 200

    def test_error_handling(self, client):
        """Test error handling and response format."""
        # Test 404 error format
        response = client.get("/nonexistent-endpoint")
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
