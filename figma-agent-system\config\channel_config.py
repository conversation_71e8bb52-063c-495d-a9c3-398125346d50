"""
Channel Configuration for Claude Talk to Figma Integration.

This module provides configuration options for managing channel communication
between the Figma Agent System and your Claude Talk to Figma MCP server.
"""

from typing import Dict, Optional
from dataclasses import dataclass

@dataclass
class ChannelConfig:
    """Configuration for channel management in Claude Talk to Figma integration."""
    
    # Your active Figma plugin channel (from your screenshot)
    DEFAULT_CHANNEL = "oa34ym6m"
    
    # Channel configuration options
    default_channel: str = DEFAULT_CHANNEL
    auto_join_channel: bool = True
    channel_timeout: float = 10.0
    
    # Agent-specific channels (optional)
    channel_per_agent: Dict[str, str] = None
    
    # Channel fallback strategy
    fallback_channels: list = None
    
    def __post_init__(self):
        """Initialize default values after dataclass creation."""
        if self.channel_per_agent is None:
            self.channel_per_agent = {}
        
        if self.fallback_channels is None:
            self.fallback_channels = [self.DEFAULT_CHANNEL, "default", "main"]

# Predefined channel configurations

# Configuration 1: Single Channel (Recommended)
# All agents use your active Figma plugin channel
SINGLE_CHANNEL_CONFIG = ChannelConfig(
    default_channel="oa34ym6m",  # Your active channel
    auto_join_channel=True,
    channel_timeout=10.0
)

# Configuration 2: Agent-Specific Channels
# Different agents can use different channels if needed
MULTI_CHANNEL_CONFIG = ChannelConfig(
    default_channel="oa34ym6m",  # Your active channel
    channel_per_agent={
        "design_agent": "oa34ym6m",      # Your active channel for design
        "color_agent": "oa34ym6m",       # Same channel for color operations
        "text_agent": "oa34ym6m",        # Same channel for text operations
        "component_agent": "oa34ym6m",   # Same channel for components
        "image_agent": "oa34ym6m",       # Same channel for images
        "prototype_agent": "oa34ym6m",   # Same channel for prototyping
        "export_agent": "oa34ym6m",      # Same channel for exports
        "collaboration_agent": "oa34ym6m" # Same channel for collaboration
    },
    auto_join_channel=True,
    channel_timeout=10.0
)

# Configuration 3: Development/Testing Channels
# For testing with different channels
DEVELOPMENT_CONFIG = ChannelConfig(
    default_channel="oa34ym6m",  # Your active channel
    channel_per_agent={
        "design_agent": "oa34ym6m",      # Production channel
        "color_agent": "test-channel",   # Test channel for color operations
    },
    fallback_channels=["oa34ym6m", "test-channel", "default"],
    auto_join_channel=True,
    channel_timeout=15.0  # Longer timeout for development
)

def get_channel_config(config_type: str = "single") -> ChannelConfig:
    """
    Get a predefined channel configuration.
    
    Args:
        config_type: Type of configuration ("single", "multi", "development")
        
    Returns:
        ChannelConfig instance
    """
    configs = {
        "single": SINGLE_CHANNEL_CONFIG,
        "multi": MULTI_CHANNEL_CONFIG,
        "development": DEVELOPMENT_CONFIG
    }
    
    return configs.get(config_type, SINGLE_CHANNEL_CONFIG)

def create_custom_channel_config(
    default_channel: str = "oa34ym6m",
    agent_channels: Dict[str, str] = None,
    timeout: float = 10.0
) -> ChannelConfig:
    """
    Create a custom channel configuration.
    
    Args:
        default_channel: Default channel ID to use
        agent_channels: Optional mapping of agent names to channels
        timeout: Channel operation timeout
        
    Returns:
        Custom ChannelConfig instance
    """
    return ChannelConfig(
        default_channel=default_channel,
        channel_per_agent=agent_channels or {},
        auto_join_channel=True,
        channel_timeout=timeout
    )

# Channel management utilities

def validate_channel_id(channel_id: str) -> bool:
    """
    Validate a channel ID format.
    
    Args:
        channel_id: Channel ID to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not channel_id or not isinstance(channel_id, str):
        return False
    
    # Basic validation - channel IDs should be non-empty strings
    # Add more specific validation rules if needed
    return len(channel_id.strip()) > 0

def get_agent_channel(agent_name: str, config: ChannelConfig) -> str:
    """
    Get the channel ID for a specific agent.
    
    Args:
        agent_name: Name of the agent
        config: Channel configuration
        
    Returns:
        Channel ID for the agent
    """
    if agent_name in config.channel_per_agent:
        return config.channel_per_agent[agent_name]
    
    return config.default_channel

def list_configured_channels(config: ChannelConfig) -> Dict[str, str]:
    """
    List all configured channels.
    
    Args:
        config: Channel configuration
        
    Returns:
        Dictionary mapping agents to their channels
    """
    channels = {"default": config.default_channel}
    channels.update(config.channel_per_agent)
    
    return channels

# Example usage and documentation

CHANNEL_USAGE_EXAMPLES = {
    "basic_usage": """
# Basic usage with your active Figma plugin channel
from config.channel_config import get_channel_config
from tools.figma_mcp_tools import get_claude_talk_to_figma_client

# Get single channel configuration (recommended)
channel_config = get_channel_config("single")

# Create MCP client with your channel
client = get_claude_talk_to_figma_client(
    channel_id="1re91tfd",  # Your active channel
    channel_per_agent={}
)
""",
    
    "agent_specific": """
# Agent-specific channel configuration
from config.channel_config import MULTI_CHANNEL_CONFIG
from tools.figma_mcp_tools import get_claude_talk_to_figma_client

# Create client with agent-specific channels
client = get_claude_talk_to_figma_client(
    channel_id="1re91tfd",
    channel_per_agent={
        "design_agent": "1re91tfd",
        "color_agent": "1re91tfd"
    }
)
""",
    
    "custom_config": """
# Custom channel configuration
from config.channel_config import create_custom_channel_config
from tools.figma_mcp_tools import FigmaMCPConfig, FigmaMCPClient

# Create custom configuration
channel_config = create_custom_channel_config(
    default_channel="1re91tfd",  # Your active channel
    agent_channels={
        "design_agent": "1re91tfd"
    },
    timeout=15.0
)

# Use in MCP configuration
mcp_config = FigmaMCPConfig(
    server_url="http://localhost:3055",
    default_channel=channel_config.default_channel,
    channel_per_agent=channel_config.channel_per_agent,
    channel_timeout=channel_config.channel_timeout
)

client = FigmaMCPClient(mcp_config)
"""
}

# Configuration validation

def validate_channel_config(config: ChannelConfig) -> Dict[str, any]:
    """
    Validate a channel configuration.
    
    Args:
        config: Channel configuration to validate
        
    Returns:
        Validation results
    """
    results = {
        "valid": True,
        "errors": [],
        "warnings": []
    }
    
    # Validate default channel
    if not validate_channel_id(config.default_channel):
        results["valid"] = False
        results["errors"].append("Invalid default channel ID")
    
    # Validate agent-specific channels
    for agent, channel in config.channel_per_agent.items():
        if not validate_channel_id(channel):
            results["valid"] = False
            results["errors"].append(f"Invalid channel ID for agent {agent}: {channel}")
    
    # Check timeout values
    if config.channel_timeout <= 0:
        results["valid"] = False
        results["errors"].append("Channel timeout must be positive")
    
    # Warnings
    if config.default_channel != "1re91tfd":
        results["warnings"].append(
            f"Default channel '{config.default_channel}' differs from your active Figma plugin channel '1re91tfd'"
        )
    
    return results
