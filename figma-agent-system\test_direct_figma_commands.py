#!/usr/bin/env python3
"""
Test Direct Figma Commands through Claude Talk to Figma Channel

This script tries different approaches to send commands directly to the Figma plugin
through the established channel connection.
"""

import asyncio
import json
import websockets
from datetime import datetime

# Configuration
WEBSOCKET_URL = "ws://localhost:3055"
CHANNEL_ID = "oa34ym6m"

async def test_direct_figma_commands():
    """Test sending direct commands to Figma plugin."""
    print(f"🎯 Testing Direct Figma Commands")
    print(f"Channel: {CHANNEL_ID}")
    print("=" * 70)
    
    try:
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            print(f"✅ Connected to {WEBSOCKET_URL}")
            
            # Wait for initial message
            initial_message = await websocket.recv()
            print(f"📨 Initial: {initial_message}")
            
            # Join channel
            join_message = {"type": "join", "channel": CHANNEL_ID}
            await websocket.send(json.dumps(join_message))
            
            join_response = await websocket.recv()
            print(f"✅ Joined: {join_response}")
            
            # Test different command formats
            commands_to_test = [
                {
                    "name": "Direct Plugin Message",
                    "message": {
                        "type": "message",
                        "channel": CHANNEL_ID,
                        "content": "Create a frame with width 400 and height 300"
                    }
                },
                {
                    "name": "Figma API Command",
                    "message": {
                        "type": "figma_api",
                        "command": "createFrame",
                        "parameters": {
                            "width": 400,
                            "height": 300,
                            "name": "Test Frame from Agent"
                        }
                    }
                },
                {
                    "name": "Plugin Code Execution",
                    "message": {
                        "type": "execute",
                        "code": "figma.createFrame()",
                        "parameters": {
                            "width": 400,
                            "height": 300
                        }
                    }
                },
                {
                    "name": "Simple Command",
                    "message": {
                        "command": "create_frame",
                        "width": 400,
                        "height": 300,
                        "name": "Agent Test Frame"
                    }
                },
                {
                    "name": "Channel Message",
                    "message": {
                        "type": "channel_message",
                        "channel": CHANNEL_ID,
                        "action": "create_frame",
                        "data": {
                            "width": 400,
                            "height": 300,
                            "name": "Channel Test Frame"
                        }
                    }
                },
                {
                    "name": "Figma Plugin Command",
                    "message": {
                        "type": "plugin_command",
                        "plugin": "figma",
                        "method": "createFrame",
                        "args": {
                            "width": 400,
                            "height": 300,
                            "name": "Plugin Command Frame"
                        }
                    }
                },
                {
                    "name": "Raw Figma Code",
                    "message": {
                        "type": "figma_code",
                        "code": """
                        const frame = figma.createFrame();
                        frame.name = "Agent Created Frame";
                        frame.resize(400, 300);
                        frame.x = 100;
                        frame.y = 100;
                        figma.currentPage.appendChild(frame);
                        figma.viewport.scrollAndZoomIntoView([frame]);
                        """
                    }
                },
                {
                    "name": "MCP Standard Format",
                    "message": {
                        "jsonrpc": "2.0",
                        "method": "tools/call",
                        "params": {
                            "name": "create_frame",
                            "arguments": {
                                "width": 400,
                                "height": 300,
                                "name": "MCP Standard Frame"
                            }
                        },
                        "id": "create_frame_001"
                    }
                }
            ]
            
            successful_commands = []
            
            for i, cmd in enumerate(commands_to_test):
                print(f"\n🧪 Test {i+1}: {cmd['name']}")
                print(f"   Message: {json.dumps(cmd['message'], indent=4)}")
                
                try:
                    # Send command
                    await websocket.send(json.dumps(cmd['message']))
                    print(f"   📤 Sent command")
                    
                    # Wait for response
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        response_data = json.loads(response)
                        print(f"   ✅ Response: {json.dumps(response_data, indent=6)}")
                        
                        # Check if this looks like a successful response
                        if (response_data.get("type") != "system" or 
                            "error" not in str(response_data).lower()):
                            successful_commands.append(cmd['name'])
                            
                    except asyncio.TimeoutError:
                        print(f"   ⏰ No response (timeout)")
                    
                except Exception as e:
                    print(f"   ❌ Error sending: {e}")
                
                # Pause between commands
                await asyncio.sleep(1)
            
            # Summary
            print(f"\n📊 COMMAND TEST SUMMARY")
            print("=" * 70)
            print(f"Commands tested: {len(commands_to_test)}")
            print(f"Successful responses: {len(successful_commands)}")
            
            if successful_commands:
                print(f"✅ Commands that got responses:")
                for cmd in successful_commands:
                    print(f"   - {cmd}")
            else:
                print(f"❌ No commands got meaningful responses")
            
            return successful_commands
            
    except Exception as e:
        print(f"❌ Direct command test failed: {e}")
        import traceback
        traceback.print_exc()
        return []

async def test_interactive_session():
    """Test an interactive session with the Figma plugin."""
    print(f"\n💬 Testing Interactive Session")
    print("=" * 70)
    
    try:
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            # Join channel
            join_message = {"type": "join", "channel": CHANNEL_ID}
            await websocket.send(json.dumps(join_message))
            await websocket.recv()  # Join confirmation
            
            # Send a simple message
            simple_message = {
                "type": "message",
                "content": "Hello Figma! Can you create a frame?",
                "channel": CHANNEL_ID,
                "timestamp": datetime.now().isoformat()
            }
            
            print(f"💬 Sending: {simple_message['content']}")
            await websocket.send(json.dumps(simple_message))
            
            # Wait for any response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"✅ Response: {response}")
            except asyncio.TimeoutError:
                print(f"⏰ No response to simple message")
            
            # Try sending a direct instruction
            instruction = {
                "type": "instruction",
                "action": "create_frame",
                "parameters": {
                    "width": 400,
                    "height": 300,
                    "name": "Interactive Test Frame",
                    "x": 200,
                    "y": 200
                },
                "channel": CHANNEL_ID
            }
            
            print(f"📋 Sending instruction...")
            await websocket.send(json.dumps(instruction))
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"✅ Instruction response: {response}")
            except asyncio.TimeoutError:
                print(f"⏰ No response to instruction")
                
    except Exception as e:
        print(f"❌ Interactive session failed: {e}")

async def test_figma_plugin_api():
    """Test if we can call Figma Plugin API directly."""
    print(f"\n🔌 Testing Figma Plugin API Calls")
    print("=" * 70)
    
    try:
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            # Join channel
            join_message = {"type": "join", "channel": CHANNEL_ID}
            await websocket.send(json.dumps(join_message))
            await websocket.recv()
            
            # Test Figma Plugin API patterns
            api_calls = [
                {
                    "name": "figma.createFrame()",
                    "call": {
                        "type": "api_call",
                        "api": "figma",
                        "method": "createFrame",
                        "channel": CHANNEL_ID
                    }
                },
                {
                    "name": "figma.createRectangle()",
                    "call": {
                        "type": "api_call", 
                        "api": "figma",
                        "method": "createRectangle",
                        "channel": CHANNEL_ID
                    }
                },
                {
                    "name": "Plugin eval",
                    "call": {
                        "type": "eval",
                        "code": "const frame = figma.createFrame(); frame.name = 'Eval Frame'; frame.resize(400, 300);",
                        "channel": CHANNEL_ID
                    }
                }
            ]
            
            for api_call in api_calls:
                print(f"🔧 Testing: {api_call['name']}")
                await websocket.send(json.dumps(api_call['call']))
                
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    print(f"   ✅ Response: {response}")
                except asyncio.TimeoutError:
                    print(f"   ⏰ Timeout")
                
                await asyncio.sleep(1)
                
    except Exception as e:
        print(f"❌ Plugin API test failed: {e}")

async def main():
    """Main test function."""
    print("🚀 Direct Figma Command Testing")
    print(f"Timestamp: {datetime.now()}")
    print("=" * 80)
    
    # Test direct commands
    successful_commands = await test_direct_figma_commands()
    
    # Test interactive session
    await test_interactive_session()
    
    # Test plugin API
    await test_figma_plugin_api()
    
    # Final recommendations
    print(f"\n🎯 FINAL RECOMMENDATIONS")
    print("=" * 80)
    
    if successful_commands:
        print(f"✅ Some commands got responses!")
        print(f"💡 Try using these command formats in your agents:")
        for cmd in successful_commands:
            print(f"   - {cmd}")
    else:
        print(f"❌ No commands got meaningful responses")
        print(f"💡 Possible next steps:")
        print(f"1. Check Claude Talk to Figma documentation")
        print(f"2. Look at the plugin source code for supported commands")
        print(f"3. Try different message formats")
        print(f"4. Check if the plugin needs to be in a specific mode")
    
    print(f"\n🔍 What we know:")
    print(f"✅ WebSocket connection works")
    print(f"✅ Channel joining works (channel: {CHANNEL_ID})")
    print(f"✅ Plugin is connected and visible in Figma")
    print(f"❓ Plugin command protocol is unclear")
    
    print(f"\n💡 Recommendation:")
    print(f"Check your Claude Talk to Figma plugin documentation or source code")
    print(f"to find the correct command format for creating frames.")

if __name__ == "__main__":
    asyncio.run(main())
