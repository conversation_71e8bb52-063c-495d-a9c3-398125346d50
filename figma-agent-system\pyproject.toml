[project]
name = "figma-agent-system"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "aiofiles>=24.1.0",
    "fastapi>=0.115.14",
    "google-generativeai>=0.8.5",
    "httpx>=0.28.1",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.0.0",
    "python-dotenv>=1.1.1",
    "uvicorn>=0.35.0",
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "websockets>=15.0.1",
]
