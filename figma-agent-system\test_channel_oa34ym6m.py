#!/usr/bin/env python3
"""
Channel Connectivity Test for Updated Channel ID: oa34ym6m

This script tests the connectivity to your correct active Figma plugin channel.
"""

import asyncio
import websockets
import json
import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Set test environment variables
os.environ["GEMINI_API_KEY"] = "test-key"
os.environ["FIGMA_ACCESS_TOKEN"] = "test-token"

CORRECT_CHANNEL_ID = "oa34ym6m"  # Your actual active Figma plugin channel
WS_URL = "ws://localhost:3055"

async def test_correct_channel_connection():
    """Test connection to the correct channel ID."""
    print(f"🔌 Testing Connection to Correct Channel: {CORRECT_CHANNEL_ID}")
    print("=" * 60)
    
    try:
        async with websockets.connect(WS_URL) as websocket:
            print(f"✅ Connected to {WS_URL}")
            
            # Receive system message
            system_message = await websocket.recv()
            system_data = json.loads(system_message)
            print(f"📨 System message: {system_data}")
            
            # Join the correct channel
            join_message = {
                "type": "join",
                "channel": CORRECT_CHANNEL_ID
            }
            
            print(f"📤 Sending join message for channel: {CORRECT_CHANNEL_ID}")
            await websocket.send(json.dumps(join_message))
            
            # Wait for join response
            try:
                join_response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                join_data = json.loads(join_response)
                print(f"📨 Join response: {join_data}")
                
                if join_data.get("channel") == CORRECT_CHANNEL_ID:
                    print(f"✅ Successfully joined channel: {CORRECT_CHANNEL_ID}")
                    return True
                else:
                    print(f"⚠️  Joined different channel: {join_data.get('channel')}")
                    return False
                    
            except asyncio.TimeoutError:
                print("⏰ Join response timeout")
                return False
                
    except Exception as e:
        print(f"❌ Channel connection failed: {e}")
        return False

async def test_agent_channel_configuration():
    """Test that agents are configured with the correct channel."""
    print(f"\n🤖 Testing Agent Channel Configuration")
    print("=" * 60)
    
    try:
        from tools.figma_mcp_tools import get_claude_talk_to_figma_client
        
        # Create client with correct channel
        client = get_claude_talk_to_figma_client(channel_id=CORRECT_CHANNEL_ID)
        
        # Test channel extraction for different agents
        test_contexts = [
            {"agent_name": "design_agent"},
            {"agent_name": "color_agent"},
            {"agent_name": "text_agent"},
            {"agent_name": "component_agent"},
            {"channel_id": CORRECT_CHANNEL_ID}  # Explicit channel override
        ]
        
        print(f"🔍 Testing channel routing for different agents:")
        all_correct = True
        
        for context in test_contexts:
            channel = client._get_channel_for_context(context)
            agent_name = context.get("agent_name", "explicit_override")
            status = "✅" if channel == CORRECT_CHANNEL_ID else "❌"
            
            if channel != CORRECT_CHANNEL_ID:
                all_correct = False
            
            print(f"   {status} {agent_name}: {channel}")
        
        await client.close()
        
        if all_correct:
            print(f"\n✅ All agents correctly configured for channel: {CORRECT_CHANNEL_ID}")
        else:
            print(f"\n❌ Some agents have incorrect channel configuration!")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Agent configuration test failed: {e}")
        return False

async def test_mcp_client_factory():
    """Test MCP client factory functions with correct channel."""
    print(f"\n🏭 Testing MCP Client Factory Functions")
    print("=" * 60)
    
    try:
        from tools.figma_mcp_tools import get_claude_talk_to_figma_client, get_figma_mcp_client
        
        # Test default factory function
        print("🧪 Testing get_claude_talk_to_figma_client()...")
        client1 = get_claude_talk_to_figma_client()
        default_channel = client1.config.default_channel
        
        if default_channel == CORRECT_CHANNEL_ID:
            print(f"✅ Default factory uses correct channel: {default_channel}")
        else:
            print(f"❌ Default factory uses wrong channel: {default_channel}")
            print(f"   Expected: {CORRECT_CHANNEL_ID}")
        
        await client1.close()
        
        # Test explicit channel specification
        print("🧪 Testing explicit channel specification...")
        client2 = get_claude_talk_to_figma_client(channel_id=CORRECT_CHANNEL_ID)
        explicit_channel = client2.config.default_channel
        
        if explicit_channel == CORRECT_CHANNEL_ID:
            print(f"✅ Explicit factory uses correct channel: {explicit_channel}")
        else:
            print(f"❌ Explicit factory uses wrong channel: {explicit_channel}")
        
        await client2.close()
        
        # Test general factory function
        print("🧪 Testing get_figma_mcp_client()...")
        client3 = get_figma_mcp_client()
        general_channel = client3.config.default_channel
        
        if general_channel == CORRECT_CHANNEL_ID:
            print(f"✅ General factory uses correct channel: {general_channel}")
        else:
            print(f"❌ General factory uses wrong channel: {general_channel}")
        
        await client3.close()
        
        return (default_channel == CORRECT_CHANNEL_ID and 
                explicit_channel == CORRECT_CHANNEL_ID and 
                general_channel == CORRECT_CHANNEL_ID)
        
    except Exception as e:
        print(f"❌ Factory function test failed: {e}")
        return False

async def test_channel_configuration_files():
    """Test channel configuration in config files."""
    print(f"\n📁 Testing Channel Configuration Files")
    print("=" * 60)
    
    try:
        from config.channel_config import (
            SINGLE_CHANNEL_CONFIG, 
            MULTI_CHANNEL_CONFIG, 
            get_channel_config,
            create_custom_channel_config
        )
        
        # Test single channel config
        single_config = SINGLE_CHANNEL_CONFIG
        if single_config.default_channel == CORRECT_CHANNEL_ID:
            print(f"✅ SINGLE_CHANNEL_CONFIG uses correct channel: {single_config.default_channel}")
        else:
            print(f"❌ SINGLE_CHANNEL_CONFIG uses wrong channel: {single_config.default_channel}")
        
        # Test multi channel config
        multi_config = MULTI_CHANNEL_CONFIG
        if multi_config.default_channel == CORRECT_CHANNEL_ID:
            print(f"✅ MULTI_CHANNEL_CONFIG uses correct default channel: {multi_config.default_channel}")
        else:
            print(f"❌ MULTI_CHANNEL_CONFIG uses wrong default channel: {multi_config.default_channel}")
        
        # Test get_channel_config function
        config = get_channel_config("single")
        if config.default_channel == CORRECT_CHANNEL_ID:
            print(f"✅ get_channel_config('single') returns correct channel: {config.default_channel}")
        else:
            print(f"❌ get_channel_config('single') returns wrong channel: {config.default_channel}")
        
        # Test custom config creation
        custom_config = create_custom_channel_config()
        if custom_config.default_channel == CORRECT_CHANNEL_ID:
            print(f"✅ create_custom_channel_config() uses correct default: {custom_config.default_channel}")
        else:
            print(f"❌ create_custom_channel_config() uses wrong default: {custom_config.default_channel}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration file test failed: {e}")
        return False

def print_summary(results):
    """Print test summary."""
    print(f"\n{'='*60}")
    print(f"📊 CHANNEL CONFIGURATION TEST SUMMARY")
    print(f"{'='*60}")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print(f"\n🔍 Test Results:")
    for test_name, result in results.items():
        status = "✅" if result else "❌"
        print(f"   {status} {test_name}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ Your Figma Agent System is correctly configured for channel: {CORRECT_CHANNEL_ID}")
        print(f"✅ All agents will use the correct active Figma plugin channel")
        print(f"✅ WebSocket communication is properly configured")
    else:
        print(f"\n⚠️  SOME TESTS FAILED!")
        print(f"🔧 Please check the failed tests above and update configurations")
    
    print(f"\n🚀 Next Steps:")
    print(f"1. Ensure your Claude Talk to Figma MCP server is running on port 3055")
    print(f"2. Verify your Figma plugin is active on channel: {CORRECT_CHANNEL_ID}")
    print(f"3. Test the complete system with real Figma operations")

async def main():
    """Run all channel configuration tests."""
    print("🚀 Channel Configuration Test - Updated Channel ID")
    print(f"Testing configuration for channel: {CORRECT_CHANNEL_ID}")
    print("=" * 80)
    
    results = {}
    
    # Test 1: Channel connectivity
    results["Channel Connectivity"] = await test_correct_channel_connection()
    
    # Test 2: Agent configuration
    results["Agent Configuration"] = await test_agent_channel_configuration()
    
    # Test 3: Factory functions
    results["Factory Functions"] = await test_mcp_client_factory()
    
    # Test 4: Configuration files
    results["Configuration Files"] = await test_channel_configuration_files()
    
    # Print summary
    print_summary(results)

if __name__ == "__main__":
    asyncio.run(main())
