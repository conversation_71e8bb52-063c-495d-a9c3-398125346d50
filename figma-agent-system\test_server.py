#!/usr/bin/env python3
"""
Simple test script to verify the FastAPI server can start.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Set test environment variables
os.environ["GEMINI_API_KEY"] = "test-key"
os.environ["FIGMA_ACCESS_TOKEN"] = "test-token"

def test_app_creation():
    """Test that the FastAPI app can be created without errors."""
    try:
        print("🔧 Testing FastAPI app creation...")
        from api.fastapi_server import app
        print("✅ FastAPI app created successfully!")
        
        print("🔧 Testing app configuration...")
        print(f"   - App title: {app.title}")
        print(f"   - App version: {app.version}")
        print(f"   - Routes count: {len(app.routes)}")
        
        # List all routes
        print("📋 Available routes:")
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                methods = ', '.join(route.methods) if route.methods else 'N/A'
                print(f"   - {methods} {route.path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating FastAPI app: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_initialization():
    """Test that all agents can be initialized."""
    try:
        print("\n🤖 Testing agent initialization...")
        
        from agents.design_agent import DesignAgent
        from agents.component_agent import ComponentAgent
        from agents.text_agent import TextAgent
        from agents.color_agent import ColorAgent
        from agents.image_agent import ImageAgent
        from agents.prototype_agent import PrototypeAgent
        from agents.export_agent import ExportAgent
        from agents.collaboration_agent import CollaborationAgent
        
        agents = [
            ("DesignAgent", DesignAgent),
            ("ComponentAgent", ComponentAgent),
            ("TextAgent", TextAgent),
            ("ColorAgent", ColorAgent),
            ("ImageAgent", ImageAgent),
            ("PrototypeAgent", PrototypeAgent),
            ("ExportAgent", ExportAgent),
            ("CollaborationAgent", CollaborationAgent),
        ]
        
        for name, agent_class in agents:
            try:
                # Agents have their own constructors, no parameters needed
                agent = agent_class()
                print(f"   ✅ {name} initialized successfully")
            except Exception as e:
                print(f"   ❌ {name} failed: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing agents: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Figma Multi-Agent System Tests...")
    print("=" * 60)
    
    success = True
    
    # Test app creation
    if not test_app_creation():
        success = False
    
    # Test agent initialization
    if not test_agent_initialization():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests passed! The system is ready to run.")
    else:
        print("❌ Some tests failed. Check the errors above.")
        sys.exit(1)
