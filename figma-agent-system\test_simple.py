#!/usr/bin/env python3
"""Simple test to identify where the system is hanging."""

import asyncio
import sys

print("Starting simple test...")

try:
    print("1. Testing imports...")
    from config.settings import Settings
    print("   ✅ Settings imported")
    
    from utils.logging_config import setup_logging
    print("   ✅ Logging imported")
    
    from agents.design_agent import DesignAgent
    print("   ✅ DesignAgent imported")
    
    from agents.text_agent import TextAgent
    print("   ✅ TextAgent imported")
    
    from agents.color_agent import ColorAgent
    print("   ✅ ColorAgent imported")
    
    from agents.component_agent import ComponentAgent
    print("   ✅ ComponentAgent imported")
    
    from router.agent_router import AgentRouter
    print("   ✅ AgentRouter imported")
    
    print("2. Testing settings initialization...")
    settings = Settings()
    print("   ✅ Settings initialized")
    
    print("3. Testing logging setup...")
    setup_logging()
    print("   ✅ Logging setup complete")
    
    print("4. Testing agent creation...")
    design_agent = DesignAgent()
    print("   ✅ DesignAgent created")
    
    text_agent = TextAgent()
    print("   ✅ TextAgent created")
    
    color_agent = ColorAgent()
    print("   ✅ ColorAgent created")
    
    component_agent = ComponentAgent()
    print("   ✅ ComponentAgent created")
    
    print("5. Testing router creation...")
    agents = [design_agent, text_agent, color_agent, component_agent]
    router = AgentRouter(agents=agents)
    print("   ✅ AgentRouter created")
    
    print("6. Testing async initialization...")
    
    async def test_async():
        print("   6a. Initializing agents...")
        await design_agent.initialize()
        print("      ✅ DesignAgent initialized")
        
        await text_agent.initialize()
        print("      ✅ TextAgent initialized")
        
        await color_agent.initialize()
        print("      ✅ ColorAgent initialized")
        
        await component_agent.initialize()
        print("      ✅ ComponentAgent initialized")
        
        print("   6b. Initializing router...")
        await router.initialize()
        print("      ✅ Router initialized")
        
        print("   6c. Testing simple request...")
        result = await router.route_request("create a simple frame", {})
        print(f"      ✅ Request processed: {result.get('success', False)}")
        
        print("   6d. Shutting down...")
        await router.shutdown()
        print("      ✅ Router shutdown")
        
        for agent in agents:
            await agent.shutdown()
        print("      ✅ All agents shutdown")
    
    print("   Running async test...")
    asyncio.run(test_async())
    print("   ✅ Async test complete")
    
    print("\n🎉 All tests passed! System is working correctly.")
    
except Exception as e:
    print(f"\n❌ Test failed at step: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
