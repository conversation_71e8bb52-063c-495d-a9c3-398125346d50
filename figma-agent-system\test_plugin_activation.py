#!/usr/bin/env python3
"""
Test Plugin Activation and Direct Communication

Try to activate the Figma plugin and establish direct communication
for executing Figma operations.
"""

import asyncio
import json
import websockets
from datetime import datetime

# Configuration
WEBSOCKET_URL = "ws://localhost:3055"
CHANNEL_ID = "oa34ym6m"

async def test_plugin_activation():
    """Test different ways to activate the plugin."""
    print(f"🔌 Testing Plugin Activation")
    print(f"Channel: {CHANNEL_ID}")
    print("=" * 70)
    
    try:
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            print(f"✅ Connected to {WEBSOCKET_URL}")
            
            # Join channel
            join_message = {"type": "join", "channel": CHANNEL_ID}
            await websocket.send(json.dumps(join_message))
            join_response = await websocket.recv()
            print(f"✅ Joined: {join_response}")
            
            # Try different activation messages
            activation_messages = [
                {
                    "name": "Activate Plugin",
                    "message": {
                        "type": "activate",
                        "channel": CHANNEL_ID
                    }
                },
                {
                    "name": "Start Session",
                    "message": {
                        "type": "start_session",
                        "channel": CHANNEL_ID
                    }
                },
                {
                    "name": "Enable API",
                    "message": {
                        "type": "enable_api",
                        "channel": CHANNEL_ID
                    }
                },
                {
                    "name": "Ready Signal",
                    "message": {
                        "type": "ready",
                        "channel": CHANNEL_ID
                    }
                },
                {
                    "name": "Handshake",
                    "message": {
                        "type": "handshake",
                        "channel": CHANNEL_ID,
                        "client": "figma-agent-system"
                    }
                },
                {
                    "name": "Initialize",
                    "message": {
                        "type": "initialize",
                        "channel": CHANNEL_ID,
                        "version": "1.0.0"
                    }
                }
            ]
            
            for activation in activation_messages:
                print(f"\n🔌 Trying: {activation['name']}")
                
                try:
                    await websocket.send(json.dumps(activation['message']))
                    
                    response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    response_data = json.loads(response)
                    print(f"   ✅ Response: {json.dumps(response_data, indent=4)}")
                    
                    # If we get a different response, the plugin might be activated
                    if (response_data.get("type") != "system" or 
                        "activated" in str(response_data).lower() or
                        "ready" in str(response_data).lower()):
                        print(f"   🎉 Plugin might be activated!")
                        
                        # Try creating a frame now
                        await test_frame_creation_after_activation(websocket)
                    
                except asyncio.TimeoutError:
                    print(f"   ⏰ No response")
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                
                await asyncio.sleep(1)
                
    except Exception as e:
        print(f"❌ Plugin activation test failed: {e}")

async def test_frame_creation_after_activation(websocket):
    """Test frame creation after potential activation."""
    print(f"   🖼️  Testing frame creation after activation...")
    
    frame_command = {
        "type": "figma_api",
        "command": "createFrame",
        "parameters": {
            "width": 400,
            "height": 300,
            "name": "Post-Activation Frame"
        }
    }
    
    try:
        await websocket.send(json.dumps(frame_command))
        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
        response_data = json.loads(response)
        print(f"   📋 Frame creation response: {json.dumps(response_data, indent=6)}")
        
        if response_data.get("type") == "broadcast":
            print(f"   🎉 SUCCESS! Frame creation was broadcast!")
            return True
            
    except asyncio.TimeoutError:
        print(f"   ⏰ Frame creation timeout")
    except Exception as e:
        print(f"   ❌ Frame creation error: {e}")
    
    return False

async def test_direct_figma_communication():
    """Test direct communication patterns with Figma."""
    print(f"\n💬 Testing Direct Figma Communication")
    print("=" * 70)
    
    try:
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            # Join channel
            join_message = {"type": "join", "channel": CHANNEL_ID}
            await websocket.send(json.dumps(join_message))
            await websocket.recv()
            
            # Try direct Figma communication patterns
            direct_messages = [
                {
                    "name": "Direct Code Execution",
                    "message": {
                        "type": "execute_code",
                        "code": "const frame = figma.createFrame(); frame.name = 'Direct Code Frame'; frame.resize(400, 300);",
                        "channel": CHANNEL_ID
                    }
                },
                {
                    "name": "Figma Function Call",
                    "message": {
                        "type": "function_call",
                        "function": "figma.createFrame",
                        "arguments": [],
                        "channel": CHANNEL_ID
                    }
                },
                {
                    "name": "Plugin Message",
                    "message": {
                        "type": "plugin_message",
                        "target": "figma",
                        "action": "createFrame",
                        "data": {
                            "width": 400,
                            "height": 300,
                            "name": "Plugin Message Frame"
                        },
                        "channel": CHANNEL_ID
                    }
                },
                {
                    "name": "Command Execution",
                    "message": {
                        "type": "command",
                        "command": "createFrame",
                        "args": {
                            "width": 400,
                            "height": 300,
                            "name": "Command Frame"
                        },
                        "channel": CHANNEL_ID
                    }
                }
            ]
            
            successful_patterns = []
            
            for msg_info in direct_messages:
                print(f"💬 Testing: {msg_info['name']}")
                
                try:
                    await websocket.send(json.dumps(msg_info['message']))
                    
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    print(f"   ✅ Response: {json.dumps(response_data, indent=4)}")
                    
                    # Check for success indicators
                    if (response_data.get("type") == "broadcast" or
                        "success" in str(response_data).lower() or
                        "created" in str(response_data).lower()):
                        successful_patterns.append(msg_info['name'])
                        print(f"   🎉 SUCCESS PATTERN FOUND!")
                    
                except asyncio.TimeoutError:
                    print(f"   ⏰ Timeout")
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                
                await asyncio.sleep(1)
            
            return successful_patterns
            
    except Exception as e:
        print(f"❌ Direct communication test failed: {e}")
        return []

async def test_mcp_protocol_variations():
    """Test different MCP protocol variations."""
    print(f"\n🔧 Testing MCP Protocol Variations")
    print("=" * 70)
    
    try:
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            # Join channel
            join_message = {"type": "join", "channel": CHANNEL_ID}
            await websocket.send(json.dumps(join_message))
            await websocket.recv()
            
            # Test MCP protocol variations
            mcp_variations = [
                {
                    "name": "Standard MCP Call",
                    "message": {
                        "jsonrpc": "2.0",
                        "method": "tools/call",
                        "params": {
                            "name": "createFrame",
                            "arguments": {
                                "width": 400,
                                "height": 300,
                                "name": "MCP Frame"
                            }
                        },
                        "id": "mcp_001"
                    }
                },
                {
                    "name": "MCP with Channel",
                    "message": {
                        "jsonrpc": "2.0",
                        "method": "tools/call",
                        "params": {
                            "name": "createFrame",
                            "arguments": {
                                "width": 400,
                                "height": 300,
                                "name": "MCP Channel Frame"
                            },
                            "channel": CHANNEL_ID
                        },
                        "id": "mcp_002"
                    }
                },
                {
                    "name": "Simple Tool Call",
                    "message": {
                        "method": "createFrame",
                        "params": {
                            "width": 400,
                            "height": 300,
                            "name": "Simple Tool Frame"
                        },
                        "channel": CHANNEL_ID
                    }
                }
            ]
            
            working_protocols = []
            
            for protocol in mcp_variations:
                print(f"🔧 Testing: {protocol['name']}")
                
                try:
                    await websocket.send(json.dumps(protocol['message']))
                    
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    print(f"   ✅ Response: {json.dumps(response_data, indent=4)}")
                    
                    if (response_data.get("type") == "broadcast" or
                        "result" in response_data or
                        response_data.get("jsonrpc") == "2.0"):
                        working_protocols.append(protocol['name'])
                        print(f"   🎉 WORKING PROTOCOL!")
                    
                except asyncio.TimeoutError:
                    print(f"   ⏰ Timeout")
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                
                await asyncio.sleep(1)
            
            return working_protocols
            
    except Exception as e:
        print(f"❌ MCP protocol test failed: {e}")
        return []

async def main():
    """Main test function."""
    print("🚀 Plugin Activation and Communication Testing")
    print(f"Timestamp: {datetime.now()}")
    print("=" * 80)
    
    # Test plugin activation
    await test_plugin_activation()
    
    # Test direct communication
    successful_patterns = await test_direct_figma_communication()
    
    # Test MCP protocol variations
    working_protocols = await test_mcp_protocol_variations()
    
    # Final summary
    print(f"\n🎯 FINAL ANALYSIS")
    print("=" * 80)
    
    if successful_patterns or working_protocols:
        print(f"🎉 FOUND WORKING COMMUNICATION PATTERNS!")
        
        if successful_patterns:
            print(f"✅ Working direct patterns:")
            for pattern in successful_patterns:
                print(f"   - {pattern}")
        
        if working_protocols:
            print(f"✅ Working MCP protocols:")
            for protocol in working_protocols:
                print(f"   - {protocol}")
        
        print(f"\n💡 Next Steps:")
        print(f"1. Implement the working patterns in the design agent")
        print(f"2. Update the MCP tools to use successful formats")
        print(f"3. Test frame creation with the working patterns")
        
    else:
        print(f"❌ No working communication patterns found")
        print(f"💡 Possible issues:")
        print(f"1. Plugin might need manual activation in Figma")
        print(f"2. Plugin might be in read-only mode")
        print(f"3. Different command format might be needed")
        print(f"4. Plugin might require authentication")
    
    print(f"\n🔍 What we confirmed:")
    print(f"✅ WebSocket connection works")
    print(f"✅ Channel joining works (channel: {CHANNEL_ID})")
    print(f"✅ Plugin is connected and visible")
    print(f"❓ Plugin command execution is unclear")
    
    print(f"\n📋 Please check your Figma file to see if any frames were created!")
    print(f"💡 If no frames appear, the plugin might need manual activation or different commands.")

if __name__ == "__main__":
    asyncio.run(main())
