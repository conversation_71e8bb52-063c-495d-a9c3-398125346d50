# Claude Talk to Figma Integration Guide

## 🎯 Integration Status: COMPLETE ✅

Your Figma Agent System is now successfully integrated with your existing Claude Talk to Figma MCP server!

## 🔧 What Was Configured

### 1. **Agent Configuration**
- All agents (design_agent, color_agent, etc.) now use your Claude Talk to Figma MCP server by default
- Server URL: `http://localhost:3055` (WebSocket: `ws://localhost:3055`)
- Communication: WebSocket-first with HTTP fallback
- Timeout: 45 seconds, 2 retry attempts

### 2. **WebSocket Communication Protocol**
- ✅ Connects to `ws://localhost:3055`
- ✅ Receives system message: "Please join a channel to start communicating with Figma"
- ✅ Joins channel with: `{"type": "join", "channel": "channel_name"}`
- ✅ Sends tool requests with: `{"type": "tool_call", "tool": "tool_name", "parameters": {...}}`

### 3. **Available MCP Tools**
Your agents can now use these Figma MCP tools:
- `get_figma_data` - Retrieve Figma file/node data
- `download_figma_images` - Download images from Figma
- `figma_file_info` - Get file information
- `figma_node_info` - Get node information

## 🚀 How to Use the Integrated System

### Step 1: Ensure Prerequisites
1. **Claude Talk to Figma MCP Server Running**:
   ```bash
   # Your server should be running on port 3055
   # Status check: http://localhost:3055/status
   ```

2. **Figma Plugin Connected**:
   - Open Figma
   - Ensure your Claude Talk to Figma plugin is active
   - Verify the WebSocket connection is established (channel: 1re91tfd)

### Step 2: Start the Figma Agent System
```bash
cd figma-agent-system
uvicorn api.fastapi_server:app --reload --port 8000
```

### Step 3: Send Requests to Agents
The agents will automatically use your Claude Talk to Figma MCP tools:

```bash
# Example: Design Agent Request
curl -X POST "http://localhost:8000/agents/design/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "task_description": "Get information about the current Figma file",
    "parameters": {
      "fileKey": "your-figma-file-key"
    }
  }'
```

## 🔍 Testing the Integration

### Quick Test
```bash
cd figma-agent-system
python test_claude_talk_to_figma_integration.py
```

### WebSocket Communication Test
```bash
python test_websocket_communication.py
```

## 🛠️ Configuration Details

### Default Configuration (Automatic)
```python
# This is automatically used by all agents
config = FigmaMCPConfig(
    server_url="http://localhost:3055",
    server_port=3055,
    default_timeout=45.0,
    max_retries=2
)
```

### Custom Configuration (Optional)
```python
from tools.figma_mcp_tools import get_figma_mcp_client, FigmaMCPConfig

# Custom configuration if needed
custom_config = FigmaMCPConfig(
    server_url="http://localhost:3055",
    server_port=3055,
    default_timeout=60.0,
    max_retries=3
)

client = get_figma_mcp_client(custom_config)
```

## 📊 Architecture Overview

```
User Request → FastAPI Server → Task Router → Agent → Figma MCP Client → Claude Talk to Figma Server → Figma Plugin → Figma API
```

### Communication Flow
1. **Agent receives task** from router
2. **Figma MCP Client** connects to `ws://localhost:3055`
3. **Joins channel** for communication
4. **Sends tool request** to your MCP server
5. **MCP server** communicates with Figma plugin
6. **Figma plugin** executes operations in Figma
7. **Results** flow back through the chain

## 🎯 Agent Capabilities

### Design Agent
- Frame creation and management
- Auto-layout application
- Component organization
- **Now uses your Figma MCP tools automatically**

### Color Agent
- Color palette management
- Style application
- Theme consistency
- **Integrates with your Figma color tools**

### Text Agent
- Typography management
- Text style application
- Content updates
- **Uses your Figma text tools**

## 🔧 Troubleshooting

### Common Issues

1. **Connection Failed**
   ```
   Error: Cannot connect to MCP server at http://localhost:3055
   ```
   **Solution**: Ensure your Claude Talk to Figma MCP server is running

2. **Channel Join Failed**
   ```
   Error: Please join a channel to start communicating with Figma
   ```
   **Solution**: This is handled automatically by the integration

3. **Tool Execution Timeout**
   ```
   Error: Tool get_figma_data timed out after 45s
   ```
   **Solution**: Check Figma plugin connection and file permissions

4. **No Response from Tools**
   - Ensure Figma is open with your plugin active
   - Verify the WebSocket channel is connected
   - Check that you have access to the Figma file

### Debug Commands
```bash
# Check MCP server status
curl http://localhost:3055/status

# Test WebSocket connection
python test_websocket_communication.py

# Full integration test
python test_claude_talk_to_figma_integration.py
```

## 🎉 Success! Your Integration is Ready

### What Works Now:
✅ **Agents automatically use your Claude Talk to Figma MCP server**  
✅ **WebSocket communication is established**  
✅ **Channel joining works correctly**  
✅ **Tool discovery is functional**  
✅ **Error handling and retries are implemented**  

### Next Steps:
1. **Test with real Figma files** - Use actual file keys and node IDs
2. **Monitor server logs** - Check your MCP server logs for tool execution
3. **Verify Figma operations** - Ensure changes appear in your Figma files
4. **Scale usage** - Use the agents for your design workflows

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Run the test scripts to diagnose problems
3. Verify your Claude Talk to Figma MCP server logs
4. Ensure Figma plugin connectivity

**Your Figma Agent System is now fully integrated with Claude Talk to Figma! 🚀**
