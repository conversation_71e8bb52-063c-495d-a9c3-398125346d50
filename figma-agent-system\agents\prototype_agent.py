"""
Prototype Agent for Figma Agent System.
Handles prototyping, interactions, and flow management.
"""

import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentRequest, AgentResponse, AgentStatus, AgentCapability
from config.agent_activities import FIGMA_ACTIVITIES
from tools.mcp_integration import MC<PERSON><PERSON>, ToolRegistry, MCPTool
from tools.figma_api import FigmaAPIClient
from llm.gemini_client import GeminiClient
from utils.logging_config import create_agent_logger


class PrototypeAgent(BaseAgent):
    """
    Prototype Agent specializing in interactions and user flows.
    
    Capabilities:
    - Interaction creation and management
    - Flow design and optimization
    - Animation and transitions
    - User journey mapping
    - Prototype testing
    """
    
    def __init__(
        self,
        mcp_client: MCPClient = None,
        figma_client: FigmaAPIClient = None,
        gemini_client: GeminiClient = None
    ):
        super().__init__(
            name="prototype_agent",
            capabilities=[AgentCapability.PROTOTYPE]
        )
        
        # Initialize clients
        self.mcp_client = mcp_client or MCPClient()
        self.figma_client = figma_client or FigmaAPIClient()
        self.gemini_client = gemini_client or GeminiClient()
        
        # Initialize tool registry
        self.tool_registry = ToolRegistry(self.mcp_client)
        self._register_prototype_tools()
        
        # Agent-specific logger
        self.logger = create_agent_logger("prototype")
        
        # Prototype-specific metrics
        self.interactions_created = 0
        self.flows_designed = 0
        self.animations_added = 0
        
        self.logger.info("Prototype Agent initialized")

    def get_supported_actions(self) -> list:
        """Get list of actions this agent can handle."""
        return [
            "create_interaction", "create_overlay", "set_transition",
            "create_flow", "add_hotspot", "create_smart_animate",
            "set_scroll_behavior", "create_component_state",
            "add_device_frame", "create_presentation", "set_prototype_settings"
        ]

    def _can_handle_action(self, action: str) -> bool:
        """Check if this agent can handle the given action."""
        return action in self.get_supported_actions()

    def _register_prototype_tools(self) -> None:
        """Register prototype-related tools."""
        
        # Create interaction tool
        create_interaction_tool = MCPTool(
            name="create_interaction",
            description="Create an interaction between elements",
            parameters={
                "source_node_id": {"type": str, "required": True, "description": "Source node ID"},
                "target_node_id": {"type": str, "required": True, "description": "Target node ID"},
                "trigger": {"type": str, "required": False, "default": "ON_CLICK", "description": "Interaction trigger"},
                "action": {"type": str, "required": False, "default": "NAVIGATE", "description": "Interaction action"},
                "transition": {"type": dict, "required": False, "description": "Transition settings"}
            },
            handler=self._handle_create_interaction,
            timeout=12.0,
            max_retries=2
        )
        self.tool_registry.register_tool(create_interaction_tool, "prototype_management")
    
    async def execute_task(self, request: AgentRequest) -> AgentResponse:
        """Execute a prototype task."""
        self.logger.info(
            "Executing prototype task",
            task_id=request.task_id,
            action=request.action
        )
        
        try:
            if not self._validate_request(request):
                return AgentResponse(
                    task_id=request.task_id,
                    agent_name=self.name,
                    status=AgentStatus.FAILED,
                    error="Invalid request parameters",
                    execution_time=0.0
                )
            
            if request.action not in FIGMA_ACTIVITIES:
                return AgentResponse(
                    task_id=request.task_id,
                    agent_name=self.name,
                    status=AgentStatus.FAILED,
                    error=f"Unsupported action: {request.action}",
                    execution_time=0.0
                )
            
            activity = FIGMA_ACTIVITIES[request.action]
            
            if activity.agent_capability not in self.capabilities:
                return AgentResponse(
                    task_id=request.task_id,
                    agent_name=self.name,
                    status=AgentStatus.FAILED,
                    error=f"Action '{request.action}' not supported by Prototype Agent",
                    execution_time=0.0
                )
            
            result = await self._execute_action(request.action, request.parameters, request.context)
            self._update_task_metrics(request.action)
            
            return AgentResponse(
                task_id=request.task_id,
                agent_name=self.name,
                status=AgentStatus.COMPLETED,
                result=result,
                execution_time=result.get("execution_time", 0.0),
                metadata={
                    "action": request.action,
                    "tools_used": result.get("tools_used", []),
                    "ai_assistance": result.get("ai_assistance", False)
                }
            )
            
        except Exception as e:
            self.logger.error(
                "Prototype task execution failed",
                task_id=request.task_id,
                action=request.action,
                error=str(e)
            )
            
            return AgentResponse(
                task_id=request.task_id,
                agent_name=self.name,
                status=AgentStatus.FAILED,
                error=str(e),
                execution_time=0.0
            )
    
    async def _execute_action(
        self,
        action: str,
        parameters: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute a specific prototype action."""
        
        if action == "create_interaction":
            response = await self.tool_registry.execute_tool("create_interaction", parameters, context)
            
            if response.status == "success":
                return {
                    "success": True,
                    "result": response.result,
                    "execution_time": response.execution_time,
                    "tools_used": ["create_interaction"]
                }
            else:
                raise Exception(f"Tool execution failed: {response.error}")
        
        else:
            raise Exception(f"Unknown action: {action}")
    
    async def _handle_create_interaction(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle interaction creation."""
        source_node_id = parameters.get("source_node_id")
        target_node_id = parameters.get("target_node_id")
        
        if not source_node_id or not target_node_id:
            raise ValueError("source_node_id and target_node_id are required")
        
        interaction_data = {
            "source_node_id": source_node_id,
            "target_node_id": target_node_id,
            "trigger": parameters.get("trigger", "ON_CLICK"),
            "action": parameters.get("action", "NAVIGATE"),
            "transition": parameters.get("transition", {"type": "DISSOLVE", "duration": 300}),
            "created_at": datetime.utcnow().isoformat()
        }
        
        interaction_id = f"interaction_{int(datetime.utcnow().timestamp())}"
        self.interactions_created += 1
        
        return {
            "interaction_id": interaction_id,
            "interaction_data": interaction_data,
            "success": True
        }
    
    def _validate_request(self, request: AgentRequest) -> bool:
        """Validate prototype request."""
        if not request.task_id or not request.action:
            return False
        
        if request.action == "create_interaction":
            params = request.parameters
            if not params.get("source_node_id") or not params.get("target_node_id"):
                return False
        
        return True
    
    def _update_task_metrics(self, action: str) -> None:
        """Update task-specific metrics."""
        if action == "create_interaction":
            self.interactions_created += 1
    
    def get_agent_metrics(self) -> Dict[str, Any]:
        """Get agent-specific metrics."""
        base_metrics = super().get_agent_metrics()
        
        prototype_metrics = {
            "interactions_created": self.interactions_created,
            "flows_designed": self.flows_designed,
            "animations_added": self.animations_added
        }
        
        return {**base_metrics, **prototype_metrics}
    
    async def close(self) -> None:
        """Close agent and cleanup resources."""
        await self.tool_registry.close()
        await self.figma_client.close()
        self.logger.info("Prototype Agent closed")
