#!/usr/bin/env python3
"""
Test script for Figma MCP Tools Integration.
Helps you configure and test your specific Figma MCP tools.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Set test environment variables
os.environ["GEMINI_API_KEY"] = "test-key"
os.environ["FIGMA_ACCESS_TOKEN"] = "test-token"

async def test_figma_mcp_detection():
    """Test automatic detection of Figma MCP tools."""
    print("🔍 Testing Figma MCP Tools Detection...")
    
    try:
        from config.figma_mcp_config import FigmaMCPConfigurator
        
        configurator = FigmaMCPConfigurator()
        detection = configurator.detect_figma_mcp_setup()
        
        print(f"✅ Detection completed:")
        print(f"   MCP Server Found: {detection['mcp_server_found']}")
        print(f"   Server Type: {detection['server_type']}")
        print(f"   Server Path: {detection['server_path']}")
        print(f"   Node.js Tools: {detection['node_tools_found']}")
        print(f"   Python Tools: {detection['python_tools_found']}")
        
        print(f"\n💡 Recommendations:")
        for rec in detection['recommendations']:
            print(f"   • {rec}")
        
        return detection
        
    except Exception as e:
        print(f"❌ Detection failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_figma_mcp_client():
    """Test Figma MCP client initialization and basic functionality."""
    print("\n🔧 Testing Figma MCP Client...")
    
    try:
        from tools.figma_mcp_tools import FigmaMCPClient, FigmaMCPConfig
        
        # Test with default configuration (direct tools)
        config = FigmaMCPConfig()
        client = FigmaMCPClient(config)
        
        print(f"✅ Figma MCP client created")
        print(f"   Available tools: {len(client.list_available_tools())}")
        
        # List available tools
        tools = client.list_available_tools()
        print(f"   Tools:")
        for tool in tools:
            print(f"     - {tool}")
        
        # Test tool execution
        print(f"\n🚀 Testing tool execution...")
        
        # Test get_figma_data tool
        response = await client.execute_tool(
            "get_figma_data",
            {
                "fileKey": "test-file-123",
                "nodeId": "test-node-456"
            }
        )
        
        print(f"✅ Tool execution result:")
        print(f"   Status: {response.status}")
        print(f"   Result: {response.result}")
        print(f"   Execution time: {response.execution_time}s")
        
        await client.close()
        return True
        
    except Exception as e:
        print(f"❌ Figma MCP client test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_agent_integration():
    """Test agent integration with Figma MCP tools."""
    print("\n🤖 Testing Agent Integration with Figma MCP Tools...")
    
    try:
        from agents.design_agent import DesignAgent
        from tools.figma_mcp_tools import FigmaMCPConfig
        
        # Create agent with Figma MCP tools
        config = FigmaMCPConfig()
        agent = DesignAgent(figma_mcp_config=config)
        
        print(f"✅ Design agent created with Figma MCP tools")
        print(f"   Agent name: {agent.name}")
        print(f"   Capabilities: {agent.capabilities}")
        print(f"   Figma MCP tools available: {len(agent.figma_mcp_client.list_available_tools())}")
        
        # Test agent tool access
        figma_tools = agent.figma_mcp_client.list_available_tools()
        print(f"   Figma MCP tools:")
        for tool in figma_tools:
            print(f"     - {tool}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def print_configuration_help():
    """Print help for configuring Figma MCP tools."""
    print("\n" + "=" * 60)
    print("📚 FIGMA MCP TOOLS CONFIGURATION HELP")
    print("=" * 60)
    
    print("""
🔧 CONFIGURATION OPTIONS:

1. **MCP Server Integration** (Recommended if you have an MCP server):
   ```python
   from config.figma_mcp_config import create_custom_config
   
   # For Node.js MCP server
   config = create_custom_config(
       server_command="node",
       server_args=["path/to/your/figma-mcp-server.js"],
       timeout=45.0
   )
   
   # For Python MCP server
   config = create_custom_config(
       server_command="python", 
       server_args=["path/to/your/figma_mcp_server.py"],
       timeout=45.0
   )
   
   # For NPM package
   config = create_custom_config(
       server_command="npx",
       server_args=["your-figma-mcp-package"],
       timeout=60.0
   )
   ```

2. **Direct Tool Integration** (If you have Python functions):
   ```python
   from config.figma_mcp_config import create_custom_config
   
   config = create_custom_config(
       server_command=None,  # No server needed
       timeout=30.0
   )
   ```

3. **Custom Tool Implementation**:
   Edit `figma-agent-system/tools/figma_mcp_tools.py` and replace the mock 
   implementations in `_execute_direct_tool` method with your actual tool calls.

🚀 NEXT STEPS:

1. **Identify your Figma MCP tools setup**:
   - Do you have an MCP server file? (e.g., server.js, server.py)
   - Do you have an NPM package with Figma MCP tools?
   - Do you have Python functions for Figma operations?

2. **Configure the integration**:
   - Update the configuration in your agent initialization
   - Or modify the mock implementations with your actual tools

3. **Test the integration**:
   - Run this test script again after configuration
   - Test with real Figma file keys and node IDs

4. **Update agents**:
   - The agents will automatically use your Figma MCP tools
   - No additional changes needed once configured

📞 NEED HELP?
Please provide:
- Path to your Figma MCP tools (files/directory)
- Type of tools (Node.js server, Python functions, NPM package)
- Example of how you currently use your tools
""")

async def main():
    """Run all Figma MCP integration tests."""
    print("🚀 Starting Figma MCP Tools Integration Tests...")
    print("=" * 60)
    
    success = True
    
    # Test detection
    detection = await test_figma_mcp_detection()
    
    # Test client
    if not await test_figma_mcp_client():
        success = False
    
    # Test agent integration
    if not await test_agent_integration():
        success = False
    
    # Print configuration help
    print_configuration_help()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 All Figma MCP integration tests passed!")
        print("\n💡 The integration is ready. Configure it with your specific tools.")
    else:
        print("❌ Some tests failed. Check the errors above.")
    
    print("\n🔧 To configure your specific Figma MCP tools:")
    print("1. Identify your tools setup (server, package, or functions)")
    print("2. Update the configuration as shown in the help above")
    print("3. Replace mock implementations with your actual tools")
    print("4. Test with real Figma file keys")

if __name__ == "__main__":
    asyncio.run(main())
