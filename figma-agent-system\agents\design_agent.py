"""
Design Agent for Figma Agent System.
Handles frame creation, layout design, and spatial organization.
Communicates directly with <PERSON> to Figma plugin via WebSocket.
"""

import asyncio
import json
import websockets
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from .base_agent import BaseAgent, AgentRequest, AgentResponse, AgentStatus, AgentCapability
from tools.figma_mcp_tools import get_figma_mcp_client, FigmaMCPConfig
from tools.figma_api import FigmaAPIClient
from utils.validation import validate_coordinates, validate_dimensions, validate_color_value


class DesignAgent(BaseAgent):
    """
    Design Agent specializing in layout, frames, and structural design elements.
    
    Capabilities:
    - Frame creation and management
    - Layout design and positioning
    - Basic structural elements
    """
    
    def __init__(self, channel_id: str = "oa34ym6m"):
        super().__init__(
            name="design",
            capabilities=[AgentCapability.DESIGN]
        )
        
        self.channel_id = channel_id
        self.websocket_url = "ws://localhost:3055"
        self.logger = logging.getLogger(f"{__name__}.{self.name}")
        
        # State tracking
        self.frames_created = 0
        self.elements_positioned = 0
        
    async def initialize(self):
        """Initialize the design agent."""
        self.logger.info(f"🎨 Initializing Design Agent (channel: {self.channel_id})")

        # Initialize Figma MCP client for actual Figma operations
        self.figma_mcp_client = get_figma_mcp_client()
        self.figma_api_client = FigmaAPIClient()

        self.logger.info("✅ Design Agent ready with Figma MCP integration")
            
    async def process_request(self, request: Dict) -> Dict:
        """Process a design request."""
        task_id = request.get('task_id', 'unknown')
        action = request.get('action', 'create_frame')
        parameters = request.get('parameters', {})
        
        self.logger.info(f"🎯 Processing {action} request: {task_id}")
        
        try:
            if action == 'create_frame':
                result = await self._create_frame(parameters)
            else:
                result = await self._create_frame(parameters)  # Default to frame creation
                
            return {
                'task_id': task_id,
                'agent_name': self.name,
                'status': 'completed',
                'success': True,
                'result': result,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ Failed to process {action}: {e}")
            return {
                'task_id': task_id,
                'agent_name': self.name,
                'status': 'failed',
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _create_frame(self, parameters: Dict) -> Dict:
        """Create a frame in Figma."""
        # Extract parameters with defaults
        width = parameters.get('width', 400)
        height = parameters.get('height', 300)
        name = parameters.get('name', 'Frame')
        x = parameters.get('x', 0)
        y = parameters.get('y', 0)
        
        # Validate parameters
        if not validate_dimensions(width, height):
            raise ValueError(f"Invalid dimensions: {width}x{height}")
            
        if not validate_coordinates(x, y):
            raise ValueError(f"Invalid coordinates: ({x}, {y})")
        
        self.logger.info(f"📐 Creating frame: {name} ({width}x{height}) at ({x}, {y})")
        
        # Create frame via Figma plugin
        frame_result = await self._send_figma_command({
            "type": "create_frame",
            "data": {
                "name": name,
                "width": width,
                "height": height,
                "x": x,
                "y": y
            }
        })
        
        self.frames_created += 1
        
        return {
            'frame_id': f"frame_{int(datetime.now().timestamp())}",
            'name': name,
            'dimensions': {'width': width, 'height': height},
            'position': {'x': x, 'y': y},
            'figma_response': frame_result,
            'success': True
        }
    
    async def _send_figma_command(self, command: Dict) -> Dict:
        """Send a command to Figma using MCP tools or direct API."""
        try:
            command_type = command.get("type", "unknown")
            command_data = command.get("data", {})

            self.logger.info(f"🎯 Executing Figma command: {command_type}")

            if command_type == "create_frame":
                # Use Figma MCP client to create frame
                result = await self.figma_mcp_client.execute_tool(
                    tool_name="get_figma_data",
                    parameters={
                        "fileKey": "current",
                        "nodeId": None,  # Create new frame
                        "action": "create_frame",
                        "frameData": {
                            "name": command_data.get("name", "New Frame"),
                            "width": command_data.get("width", 400),
                            "height": command_data.get("height", 300),
                            "x": command_data.get("x", 0),
                            "y": command_data.get("y", 0)
                        }
                    }
                )

                self.logger.info(f"✅ Frame created via MCP: {result}")
                return {"status": "success", "result": result, "method": "mcp"}

            else:
                # Fallback to WebSocket for unsupported commands
                return await self._send_websocket_command(command)

        except Exception as e:
            self.logger.error(f"❌ Figma command failed: {e}")
            # Try fallback to WebSocket
            try:
                return await self._send_websocket_command(command)
            except Exception as fallback_error:
                self.logger.error(f"❌ Fallback also failed: {fallback_error}")
                return {"status": "error", "error": str(e), "fallback_error": str(fallback_error)}

    async def _send_websocket_command(self, command: Dict) -> Dict:
        """Fallback WebSocket communication method."""
        try:
            async with websockets.connect(self.websocket_url) as websocket:
                join_message = {"type": "join", "channel": self.channel_id}
                await websocket.send(json.dumps(join_message))

                join_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                self.logger.info(f"📡 Channel joined: {join_response}")

                await websocket.send(json.dumps(command))
                self.logger.info(f"� Sent WebSocket command: {command}")

                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                response_data = json.loads(response)

                self.logger.info(f"📥 Received WebSocket response: {response_data}")
                return {"status": "websocket", "result": response_data}

        except Exception as e:
            self.logger.error(f"❌ WebSocket communication failed: {e}")
            return {"status": "error", "error": str(e)}

    def _convert_to_mcp_command(self, command: Dict) -> Dict:
        """Convert generic command to MCP protocol format for Claude Talk to Figma."""
        command_type = command.get("type", "unknown")
        command_data = command.get("data", {})

        if command_type == "create_frame":
            # First try to list available tools
            return {
                "jsonrpc": "2.0",
                "id": f"frame_{int(asyncio.get_event_loop().time())}",
                "method": "tools/list",
                "params": {}
            }
        elif command_type == "ping":
            # Test command to see what the plugin responds with
            return {
                "jsonrpc": "2.0",
                "id": f"ping_{int(asyncio.get_event_loop().time())}",
                "method": "ping",
                "params": command_data
            }
        else:
            # Fallback to original format for testing
            return command
    
    async def _test_figma_connection(self):
        """Test connection to the Figma plugin."""
        try:
            result = await self._send_figma_command({
                "type": "ping",
                "data": {"message": "Connection test from Design Agent"}
            })
            return result
        except Exception as e:
            raise Exception(f"Figma connection test failed: {e}")
    
    async def shutdown(self):
        """Shutdown the design agent."""
        self.logger.info(f"🛑 Shutting down Design Agent")
        self.logger.info(f"📊 Stats: {self.frames_created} frames created")
        
    def get_capabilities(self) -> List[str]:
        """Get list of agent capabilities."""
        return [
            "create_frame",
            "position_element",
            "resize_element",
            "create_layout"
        ]
    
    def get_status(self) -> Dict:
        """Get current agent status."""
        return {
            'name': self.name,
            'status': 'active',
            'channel_id': self.channel_id,
            'frames_created': self.frames_created,
            'elements_positioned': self.elements_positioned,
            'capabilities': self.get_capabilities()
        }

    # Abstract method implementations
    async def _execute_action(self, request: AgentRequest) -> Dict[str, Any]:
        """Execute the specific action requested."""
        action = request.action
        parameters = request.parameters

        if action == 'create_frame':
            return await self._create_frame(parameters)
        else:
            # Default to frame creation for any design action
            return await self._create_frame(parameters)

    def _can_handle_action(self, action: str) -> bool:
        """Check if this agent can handle the specified action."""
        supported_actions = self.get_supported_actions()
        return action in supported_actions

    def get_supported_actions(self) -> List[str]:
        """Get list of actions this agent supports."""
        return [
            "create_frame",
            "position_element",
            "resize_element",
            "create_layout"
        ]
