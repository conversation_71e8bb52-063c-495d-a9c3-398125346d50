"""
Design Agent for Figma Agent System.
Handles frame creation, layout design, and spatial organization.
Communicates directly with <PERSON> to Figma plugin via WebSocket.
"""

import asyncio
import json
import websockets
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from .base_agent import BaseAgent, AgentRequest, AgentResponse, AgentStatus, AgentCapability
from utils.validation import validate_coordinates, validate_dimensions, validate_color_value


class DesignAgent(BaseAgent):
    """
    Design Agent specializing in layout, frames, and structural design elements.
    
    Capabilities:
    - Frame creation and management
    - Layout design and positioning
    - Basic structural elements
    """
    
    def __init__(self, channel_id: str = "oa34ym6m"):
        super().__init__(
            name="design",
            capabilities=[AgentCapability.DESIGN]
        )
        
        self.channel_id = channel_id
        self.websocket_url = "ws://localhost:3055"
        self.logger = logging.getLogger(f"{__name__}.{self.name}")
        
        # State tracking
        self.frames_created = 0
        self.elements_positioned = 0
        
    async def initialize(self):
        """Initialize the design agent."""
        self.logger.info(f"🎨 Initializing Design Agent (channel: {self.channel_id})")
        # Skip connection test during initialization to avoid hanging
        self.logger.info("✅ Design Agent ready (connection will be tested on first use)")
            
    async def process_request(self, request: Dict) -> Dict:
        """Process a design request."""
        task_id = request.get('task_id', 'unknown')
        action = request.get('action', 'create_frame')
        parameters = request.get('parameters', {})
        
        self.logger.info(f"🎯 Processing {action} request: {task_id}")
        
        try:
            if action == 'create_frame':
                result = await self._create_frame(parameters)
            else:
                result = await self._create_frame(parameters)  # Default to frame creation
                
            return {
                'task_id': task_id,
                'agent_name': self.name,
                'status': 'completed',
                'success': True,
                'result': result,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ Failed to process {action}: {e}")
            return {
                'task_id': task_id,
                'agent_name': self.name,
                'status': 'failed',
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _create_frame(self, parameters: Dict) -> Dict:
        """Create a frame in Figma."""
        # Extract parameters with defaults
        width = parameters.get('width', 400)
        height = parameters.get('height', 300)
        name = parameters.get('name', 'Frame')
        x = parameters.get('x', 0)
        y = parameters.get('y', 0)
        
        # Validate parameters
        if not validate_dimensions(width, height):
            raise ValueError(f"Invalid dimensions: {width}x{height}")
            
        if not validate_coordinates(x, y):
            raise ValueError(f"Invalid coordinates: ({x}, {y})")
        
        self.logger.info(f"📐 Creating frame: {name} ({width}x{height}) at ({x}, {y})")
        
        # Create frame via Figma plugin
        frame_result = await self._send_figma_command({
            "type": "create_frame",
            "data": {
                "name": name,
                "width": width,
                "height": height,
                "x": x,
                "y": y
            }
        })
        
        self.frames_created += 1
        
        return {
            'frame_id': f"frame_{int(datetime.now().timestamp())}",
            'name': name,
            'dimensions': {'width': width, 'height': height},
            'position': {'x': x, 'y': y},
            'figma_response': frame_result,
            'success': True
        }
    
    async def _send_figma_command(self, command: Dict) -> Dict:
        """Send a command to the Figma plugin via WebSocket."""
        try:
            async with websockets.connect(self.websocket_url) as websocket:
                # Join the channel
                join_message = {
                    "type": "join",
                    "channel": self.channel_id
                }
                await websocket.send(json.dumps(join_message))
                
                # Wait for join confirmation
                join_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                self.logger.debug(f"Joined channel: {join_response}")
                
                # Send the actual command
                await websocket.send(json.dumps(command))
                self.logger.debug(f"Sent command: {command}")
                
                # Wait for response
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                response_data = json.loads(response)
                
                self.logger.debug(f"Received response: {response_data}")
                return response_data
                
        except asyncio.TimeoutError:
            self.logger.warning("Figma command timeout - command may still be processing")
            return {"status": "timeout", "message": "Command sent but no response received"}
        except Exception as e:
            self.logger.error(f"WebSocket communication failed: {e}")
            raise e
    
    async def _test_figma_connection(self):
        """Test connection to the Figma plugin."""
        try:
            result = await self._send_figma_command({
                "type": "ping",
                "data": {"message": "Connection test from Design Agent"}
            })
            return result
        except Exception as e:
            raise Exception(f"Figma connection test failed: {e}")
    
    async def shutdown(self):
        """Shutdown the design agent."""
        self.logger.info(f"🛑 Shutting down Design Agent")
        self.logger.info(f"📊 Stats: {self.frames_created} frames created")
        
    def get_capabilities(self) -> List[str]:
        """Get list of agent capabilities."""
        return [
            "create_frame",
            "position_element",
            "resize_element",
            "create_layout"
        ]
    
    def get_status(self) -> Dict:
        """Get current agent status."""
        return {
            'name': self.name,
            'status': 'active',
            'channel_id': self.channel_id,
            'frames_created': self.frames_created,
            'elements_positioned': self.elements_positioned,
            'capabilities': self.get_capabilities()
        }

    # Abstract method implementations
    async def _execute_action(self, request: AgentRequest) -> Dict[str, Any]:
        """Execute the specific action requested."""
        action = request.action
        parameters = request.parameters

        if action == 'create_frame':
            return await self._create_frame(parameters)
        else:
            # Default to frame creation for any design action
            return await self._create_frame(parameters)

    def _can_handle_action(self, action: str) -> bool:
        """Check if this agent can handle the specified action."""
        supported_actions = self.get_supported_actions()
        return action in supported_actions

    def get_supported_actions(self) -> List[str]:
        """Get list of actions this agent supports."""
        return [
            "create_frame",
            "position_element",
            "resize_element",
            "create_layout"
        ]
