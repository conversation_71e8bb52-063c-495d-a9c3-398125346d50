"""
Design Agent for Figma Agent System.
Handles frame creation, auto layout, grid systems, and element management.
Serves as template implementation for other specialized agents.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from .base_agent import BaseAgent, AgentRequest, AgentResponse, AgentStatus, AgentCapability
from config.agent_activities import FIGMA_ACTIVITIES
from tools.mcp_integration import MC<PERSON><PERSON>, ToolRegistry, MCPTool
from tools.figma_api import FigmaAPIClient, FigmaNode, NodeType
from tools.figma_mcp_tools import get_figma_mcp_client, FigmaMCPConfig
from llm.gemini_client import GeminiClient, ContentType, DesignSuggestion
from utils.logging_config import create_agent_logger
from utils.validation import validate_coordinates, validate_dimensions, validate_color_value


class DesignAgent(BaseAgent):
    """
    Design Agent specializing in layout, frames, and structural design elements.
    
    Capabilities:
    - Frame creation and management
    - Auto layout systems
    - Grid and constraint systems
    - Element positioning and sizing
    - Layout optimization
    """
    
    def __init__(
        self,
        mcp_client: MCPClient = None,
        figma_client: FigmaAPIClient = None,
        gemini_client: GeminiClient = None,
        figma_mcp_config: FigmaMCPConfig = None
    ):
        super().__init__(
            name="design_agent",
            capabilities=[AgentCapability.DESIGN]
        )

        # Initialize clients
        self.mcp_client = mcp_client or MCPClient()
        self.figma_client = figma_client or FigmaAPIClient()
        self.gemini_client = gemini_client or GeminiClient()

        # Initialize Figma MCP tools client
        self.figma_mcp_client = get_figma_mcp_client(figma_mcp_config)

        # Initialize tool registry and register design tools
        self.tool_registry = ToolRegistry(self.mcp_client)
        self._register_design_tools()

        # Agent-specific logger
        self.logger = create_agent_logger("design")

        # Design-specific metrics
        self.frames_created = 0
        self.layouts_optimized = 0
        self.grids_applied = 0

        self.logger.info("Design Agent initialized with Figma MCP tools")
    
    def _register_design_tools(self) -> None:
        """Register all design-related tools."""
        
        # Frame creation tool
        create_frame_tool = MCPTool(
            name="create_frame",
            description="Create a new frame with specified dimensions and properties",
            parameters={
                "width": {"type": float, "required": True, "description": "Frame width"},
                "height": {"type": float, "required": True, "description": "Frame height"},
                "x": {"type": float, "required": False, "default": 0, "description": "X position"},
                "y": {"type": float, "required": False, "default": 0, "description": "Y position"},
                "name": {"type": str, "required": False, "default": "Frame", "description": "Frame name"},
                "background_color": {"type": dict, "required": False, "description": "Background color"}
            },
            handler=self._handle_create_frame,
            timeout=10.0,
            max_retries=2
        )
        self.tool_registry.register_tool(create_frame_tool, "frame_management")
        
        # Auto layout tool
        auto_layout_tool = MCPTool(
            name="apply_auto_layout",
            description="Apply auto layout to a frame with spacing and direction",
            parameters={
                "node_id": {"type": str, "required": True, "description": "Target node ID"},
                "direction": {"type": str, "required": False, "default": "VERTICAL", "description": "Layout direction"},
                "spacing": {"type": float, "required": False, "default": 10, "description": "Item spacing"},
                "padding": {"type": dict, "required": False, "description": "Padding values"},
                "alignment": {"type": str, "required": False, "default": "MIN", "description": "Alignment"}
            },
            handler=self._handle_auto_layout,
            timeout=15.0,
            max_retries=2
        )
        self.tool_registry.register_tool(auto_layout_tool, "frame_management")
        
        # Grid system tool
        grid_system_tool = MCPTool(
            name="apply_grid_system",
            description="Apply grid system to a frame",
            parameters={
                "node_id": {"type": str, "required": True, "description": "Target node ID"},
                "columns": {"type": int, "required": False, "default": 12, "description": "Number of columns"},
                "gutter": {"type": float, "required": False, "default": 20, "description": "Gutter width"},
                "margin": {"type": float, "required": False, "default": 40, "description": "Margin size"}
            },
            handler=self._handle_grid_system,
            timeout=10.0,
            max_retries=2
        )
        self.tool_registry.register_tool(grid_system_tool, "frame_management")
        
        # Element positioning tool
        position_element_tool = MCPTool(
            name="position_element",
            description="Position an element within a frame",
            parameters={
                "node_id": {"type": str, "required": True, "description": "Element node ID"},
                "x": {"type": float, "required": True, "description": "X position"},
                "y": {"type": float, "required": True, "description": "Y position"},
                "constraints": {"type": dict, "required": False, "description": "Layout constraints"}
            },
            handler=self._handle_position_element,
            timeout=8.0,
            max_retries=2
        )
        self.tool_registry.register_tool(position_element_tool, "element_manipulation")
        
        # Resize element tool
        resize_element_tool = MCPTool(
            name="resize_element",
            description="Resize an element",
            parameters={
                "node_id": {"type": str, "required": True, "description": "Element node ID"},
                "width": {"type": float, "required": False, "description": "New width"},
                "height": {"type": float, "required": False, "description": "New height"},
                "maintain_aspect_ratio": {"type": bool, "required": False, "default": False}
            },
            handler=self._handle_resize_element,
            timeout=8.0,
            max_retries=2
        )
        self.tool_registry.register_tool(resize_element_tool, "element_manipulation")
    

    
    async def _execute_action(self, request: AgentRequest) -> Dict[str, Any]:
        """Execute a specific design action."""
        action = request.action
        parameters = request.parameters
        context = request.context or {}
        
        # Map actions to tool executions
        action_tool_map = {
            "create_frame": "create_frame",
            "apply_auto_layout": "apply_auto_layout",
            "create_grid_system": "apply_grid_system",
            "position_element": "position_element",
            "resize_element": "resize_element"
        }
        
        if action in action_tool_map:
            tool_name = action_tool_map[action]
            
            # Execute tool
            response = await self.tool_registry.execute_tool(
                tool_name,
                parameters,
                context
            )
            
            if response.status == "success":
                return {
                    "success": True,
                    "result": response.result,
                    "execution_time": response.execution_time,
                    "tools_used": [tool_name]
                }
            else:
                raise Exception(f"Tool execution failed: {response.error}")
        
        # Handle complex actions that require multiple tools or AI assistance
        elif action == "optimize_layout":
            return await self._optimize_layout(parameters, context)
        
        elif action == "create_responsive_frame":
            return await self._create_responsive_frame(parameters, context)
        
        elif action == "arrange_elements":
            return await self._arrange_elements(parameters, context)
        
        else:
            raise Exception(f"Unknown action: {action}")
    
    async def _handle_create_frame(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle frame creation."""
        # Validate parameters
        width = parameters.get("width")
        height = parameters.get("height")
        
        if not validate_dimensions(width, height):
            raise ValueError("Invalid frame dimensions")
        
        x = parameters.get("x", 0)
        y = parameters.get("y", 0)
        
        if not validate_coordinates(x, y):
            raise ValueError("Invalid frame coordinates")
        
        # Create frame data
        frame_data = {
            "type": "FRAME",
            "name": parameters.get("name", "Frame"),
            "absoluteBoundingBox": {
                "x": x,
                "y": y,
                "width": width,
                "height": height
            },
            "visible": True,
            "locked": False
        }
        
        # Add background color if provided
        background_color = parameters.get("background_color")
        if background_color and validate_color_value(background_color):
            frame_data["fills"] = [{
                "type": "SOLID",
                "color": background_color,
                "opacity": 1.0
            }]
        
        # In a real implementation, this would call Figma API
        # For now, we'll simulate the creation
        frame_id = f"frame_{int(datetime.utcnow().timestamp())}"
        
        self.frames_created += 1
        
        return {
            "frame_id": frame_id,
            "frame_data": frame_data,
            "success": True
        }
    
    async def _handle_auto_layout(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle auto layout application."""
        node_id = parameters.get("node_id")
        if not node_id:
            raise ValueError("node_id is required")
        
        layout_config = {
            "layoutMode": parameters.get("direction", "VERTICAL"),
            "itemSpacing": parameters.get("spacing", 10),
            "primaryAxisAlignItems": parameters.get("alignment", "MIN"),
            "counterAxisAlignItems": "MIN"
        }
        
        # Add padding if provided
        padding = parameters.get("padding")
        if padding:
            layout_config.update({
                "paddingLeft": padding.get("left", 0),
                "paddingRight": padding.get("right", 0),
                "paddingTop": padding.get("top", 0),
                "paddingBottom": padding.get("bottom", 0)
            })
        
        self.layouts_optimized += 1
        
        return {
            "node_id": node_id,
            "layout_config": layout_config,
            "success": True
        }
    
    async def _handle_grid_system(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle grid system application."""
        node_id = parameters.get("node_id")
        if not node_id:
            raise ValueError("node_id is required")
        
        columns = parameters.get("columns", 12)
        gutter = parameters.get("gutter", 20)
        margin = parameters.get("margin", 40)
        
        grid_config = {
            "layoutGrids": [{
                "pattern": "COLUMNS",
                "sectionSize": columns,
                "gutterSize": gutter,
                "alignment": "STRETCH",
                "color": {"r": 0.96, "g": 0.26, "b": 0.21, "a": 0.1},
                "visible": True
            }],
            "paddingLeft": margin,
            "paddingRight": margin
        }
        
        self.grids_applied += 1
        
        return {
            "node_id": node_id,
            "grid_config": grid_config,
            "success": True
        }
    
    async def _handle_position_element(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle element positioning."""
        node_id = parameters.get("node_id")
        x = parameters.get("x")
        y = parameters.get("y")
        
        if not node_id:
            raise ValueError("node_id is required")
        
        if not validate_coordinates(x, y):
            raise ValueError("Invalid coordinates")
        
        position_config = {
            "x": x,
            "y": y
        }
        
        # Add constraints if provided
        constraints = parameters.get("constraints")
        if constraints:
            position_config["constraints"] = constraints
        
        return {
            "node_id": node_id,
            "position_config": position_config,
            "success": True
        }
    
    async def _handle_resize_element(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle element resizing."""
        node_id = parameters.get("node_id")
        width = parameters.get("width")
        height = parameters.get("height")
        
        if not node_id:
            raise ValueError("node_id is required")
        
        if width is not None and height is not None:
            if not validate_dimensions(width, height):
                raise ValueError("Invalid dimensions")
        
        resize_config = {}
        if width is not None:
            resize_config["width"] = width
        if height is not None:
            resize_config["height"] = height
        
        maintain_aspect = parameters.get("maintain_aspect_ratio", False)
        if maintain_aspect:
            resize_config["constraints"] = {
                "horizontal": "SCALE",
                "vertical": "SCALE"
            }
        
        return {
            "node_id": node_id,
            "resize_config": resize_config,
            "success": True
        }
    
    def _validate_request(self, request: AgentRequest) -> bool:
        """Validate agent request."""
        if not request.task_id or not request.action:
            return False
        
        # Validate parameters based on action
        if request.action == "create_frame":
            params = request.parameters
            if not params.get("width") or not params.get("height"):
                return False
        
        return True
    
    def _update_task_metrics(self, action: str) -> None:
        """Update task-specific metrics."""
        if action == "create_frame":
            self.frames_created += 1
        elif action in ["apply_auto_layout", "optimize_layout"]:
            self.layouts_optimized += 1
        elif action == "create_grid_system":
            self.grids_applied += 1
    
    def get_agent_metrics(self) -> Dict[str, Any]:
        """Get agent-specific metrics."""
        base_metrics = super().get_agent_metrics()
        
        design_metrics = {
            "frames_created": self.frames_created,
            "layouts_optimized": self.layouts_optimized,
            "grids_applied": self.grids_applied
        }
        
        return {**base_metrics, **design_metrics}
    
    async def _optimize_layout(self, parameters: Dict, context: Dict) -> Dict[str, Any]:
        """Optimize layout using AI assistance."""
        node_id = parameters.get("node_id")
        if not node_id:
            raise ValueError("node_id is required for layout optimization")

        # Get current layout information
        layout_context = {
            "node_id": node_id,
            "optimization_goals": parameters.get("goals", ["spacing", "alignment", "hierarchy"]),
            "constraints": parameters.get("constraints", {}),
            "target_audience": context.get("target_audience", "general")
        }

        # Use Gemini for layout intelligence
        layout_intelligence = await self.gemini_client.generate_layout_intelligence(
            content_type="layout_optimization",
            viewport_size=parameters.get("viewport_size", {"width": 1200, "height": 800}),
            content_amount=parameters.get("content_amount", "medium")
        )

        # Apply recommendations
        optimization_results = []

        # Apply spacing recommendations
        if layout_intelligence.spacing_recommendations:
            spacing_result = await self.tool_registry.execute_tool(
                "apply_auto_layout",
                {
                    "node_id": node_id,
                    "spacing": layout_intelligence.spacing_recommendations.get("medium_spacing", 16),
                    "padding": {
                        "top": layout_intelligence.spacing_recommendations.get("small_spacing", 8),
                        "bottom": layout_intelligence.spacing_recommendations.get("small_spacing", 8),
                        "left": layout_intelligence.spacing_recommendations.get("medium_spacing", 16),
                        "right": layout_intelligence.spacing_recommendations.get("medium_spacing", 16)
                    }
                }
            )
            optimization_results.append(spacing_result)

        # Apply grid system if recommended
        if layout_intelligence.grid_system:
            grid_result = await self.tool_registry.execute_tool(
                "apply_grid_system",
                {
                    "node_id": node_id,
                    "columns": layout_intelligence.grid_system.get("columns", 12),
                    "gutter": layout_intelligence.grid_system.get("gutter", 20),
                    "margin": layout_intelligence.grid_system.get("margin", 40)
                }
            )
            optimization_results.append(grid_result)

        self.layouts_optimized += 1

        return {
            "success": True,
            "layout_intelligence": layout_intelligence.__dict__,
            "optimization_results": [r.__dict__ for r in optimization_results],
            "ai_assistance": True,
            "tools_used": ["apply_auto_layout", "apply_grid_system"]
        }

    async def _create_responsive_frame(self, parameters: Dict, context: Dict) -> Dict[str, Any]:
        """Create responsive frame with multiple breakpoints."""
        base_width = parameters.get("base_width", 1200)
        base_height = parameters.get("base_height", 800)
        breakpoints = parameters.get("breakpoints", [
            {"name": "mobile", "width": 375, "height": 667},
            {"name": "tablet", "width": 768, "height": 1024},
            {"name": "desktop", "width": 1200, "height": 800}
        ])

        frames_created = []

        for breakpoint in breakpoints:
            frame_params = {
                "width": breakpoint["width"],
                "height": breakpoint["height"],
                "name": f"Frame - {breakpoint['name'].title()}",
                "x": len(frames_created) * (breakpoint["width"] + 50),  # Offset frames
                "y": 0
            }

            frame_result = await self.tool_registry.execute_tool(
                "create_frame",
                frame_params
            )

            if frame_result.status == "success":
                frames_created.append({
                    "breakpoint": breakpoint["name"],
                    "frame_id": frame_result.result["frame_id"],
                    "dimensions": {"width": breakpoint["width"], "height": breakpoint["height"]}
                })

        return {
            "success": True,
            "frames_created": frames_created,
            "responsive_system": True,
            "tools_used": ["create_frame"]
        }

    async def _arrange_elements(self, parameters: Dict, context: Dict) -> Dict[str, Any]:
        """Arrange elements using AI-powered layout suggestions."""
        elements = parameters.get("elements", [])
        container_id = parameters.get("container_id")
        arrangement_style = parameters.get("style", "grid")

        if not elements:
            raise ValueError("No elements provided for arrangement")

        # Get design suggestions from AI
        design_brief = f"Arrange {len(elements)} elements in a {arrangement_style} layout"
        suggestions = await self.gemini_client.generate_design_suggestions(
            design_brief=design_brief,
            target_audience=context.get("target_audience"),
            style_preferences=[arrangement_style],
            constraints={"element_count": len(elements)}
        )

        if not suggestions:
            raise Exception("Failed to generate arrangement suggestions")

        # Use the first suggestion
        suggestion = suggestions[0]
        arrangement_results = []

        # Position elements based on AI suggestion
        for i, element in enumerate(elements):
            if i < len(suggestion.elements):
                suggested_element = suggestion.elements[i]
                position = suggested_element.get("position", {"x": i * 100, "y": 0})

                position_result = await self.tool_registry.execute_tool(
                    "position_element",
                    {
                        "node_id": element.get("id"),
                        "x": position["x"],
                        "y": position["y"]
                    }
                )
                arrangement_results.append(position_result)

        return {
            "success": True,
            "arrangement_style": arrangement_style,
            "elements_arranged": len(elements),
            "ai_suggestion": suggestion.__dict__,
            "arrangement_results": [r.__dict__ for r in arrangement_results],
            "ai_assistance": True,
            "tools_used": ["position_element"]
        }

    def get_supported_actions(self) -> list:
        """Get list of supported actions."""
        return [
            "create_frame",
            "create_component",
            "create_layout",
            "apply_design_system",
            "generate_design_suggestions",
            "create_wireframe",
            "apply_theme",
            "create_responsive_layout",
            "optimize_layout",
            "create_grid_system"
        ]

    def _can_handle_action(self, action: str) -> bool:
        """Check if this agent can handle the given action."""
        return action in self.get_supported_actions()

    async def close(self) -> None:
        """Close agent and cleanup resources."""
        await self.tool_registry.close()
        await self.figma_client.close()
        self.logger.info("Design Agent closed")
