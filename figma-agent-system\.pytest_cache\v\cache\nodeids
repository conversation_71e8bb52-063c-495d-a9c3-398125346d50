["tests/test_api_endpoints.py::TestAPIEndpoints::test_api_documentation", "tests/test_api_endpoints.py::TestAPIEndpoints::test_batch_execute_success", "tests/test_api_endpoints.py::TestAPIEndpoints::test_component_agent_execute", "tests/test_api_endpoints.py::TestAPIEndpoints::test_cors_headers", "tests/test_api_endpoints.py::TestAPIEndpoints::test_design_agent_execute", "tests/test_api_endpoints.py::TestAPIEndpoints::test_endpoint_validation", "tests/test_api_endpoints.py::TestAPIEndpoints::test_error_handling", "tests/test_api_endpoints.py::TestAPIEndpoints::test_execute_task_failure", "tests/test_api_endpoints.py::TestAPIEndpoints::test_execute_task_invalid_request", "tests/test_api_endpoints.py::TestAPIEndpoints::test_execute_task_success", "tests/test_api_endpoints.py::TestAPIEndpoints::test_get_agent_details", "tests/test_api_endpoints.py::TestAPIEndpoints::test_get_figma_activity_details", "tests/test_api_endpoints.py::TestAPIEndpoints::test_get_nonexistent_agent", "tests/test_api_endpoints.py::TestAPIEndpoints::test_get_task_status_not_found", "tests/test_api_endpoints.py::TestAPIEndpoints::test_health_check", "tests/test_api_endpoints.py::TestAPIEndpoints::test_list_agents", "tests/test_api_endpoints.py::TestAPIEndpoints::test_list_figma_activities", "tests/test_api_endpoints.py::TestAPIEndpoints::test_request_validation", "tests/test_api_endpoints.py::TestAPIEndpoints::test_router_invalid_request", "tests/test_api_endpoints.py::TestAPIEndpoints::test_router_process_failure", "tests/test_api_endpoints.py::TestAPIEndpoints::test_router_process_success", "tests/test_api_endpoints.py::TestAPIEndpoints::test_system_metrics", "tests/test_api_endpoints.py::TestAPIEndpoints::test_system_status", "tests/test_api_endpoints.py::TestAPIEndpoints::test_task_status_endpoint_exists", "tests/test_api_endpoints.py::TestAPIEndpoints::test_text_agent_execute", "tests/test_base_agent.py::TestBaseAgent::test_agent_cleanup", "tests/test_base_agent.py::TestBaseAgent::test_agent_close", "tests/test_base_agent.py::TestBaseAgent::test_agent_initialization", "tests/test_base_agent.py::TestBaseAgent::test_agent_status_transitions", "tests/test_base_agent.py::TestBaseAgent::test_can_handle_action", "tests/test_base_agent.py::TestBaseAgent::test_concurrent_task_limit", "tests/test_base_agent.py::TestBaseAgent::test_execute_action", "tests/test_base_agent.py::TestBaseAgent::test_execute_task", "tests/test_base_agent.py::TestBaseAgent::test_execute_unsupported_action", "tests/test_base_agent.py::TestBaseAgent::test_performance_metrics", "tests/test_base_agent.py::TestBaseAgent::test_supported_actions", "tests/test_base_agent.py::TestBaseAgent::test_task_history", "tests/test_base_agent.py::test_agent_request_validation", "tests/test_base_agent.py::test_agent_response_validation", "tests/test_basic_integration.py::TestBasicIntegration::test_agent_capabilities", "tests/test_basic_integration.py::TestBasicIntegration::test_agent_creation", "tests/test_basic_integration.py::TestBasicIntegration::test_basic_task_execution", "tests/test_basic_integration.py::TestBasicIntegration::test_concurrent_execution", "tests/test_basic_integration.py::TestBasicIntegration::test_router_agent_selection", "tests/test_basic_integration.py::TestBasicIntegration::test_router_registration", "tests/test_basic_integration.py::TestBasicIntegration::test_router_request_processing", "tests/test_basic_integration.py::test_system_startup_shutdown", "tests/test_specialized_agents.py::TestCollaborationAgent::test_collaboration_agent_capabilities", "tests/test_specialized_agents.py::TestColorAgent::test_color_agent_capabilities", "tests/test_specialized_agents.py::TestComponentAgent::test_component_agent_capabilities", "tests/test_specialized_agents.py::TestDesignAgent::test_create_frame_action", "tests/test_specialized_agents.py::TestDesignAgent::test_design_agent_capabilities", "tests/test_specialized_agents.py::TestExportAgent::test_export_agent_capabilities", "tests/test_specialized_agents.py::TestImageAgent::test_image_agent_capabilities", "tests/test_specialized_agents.py::TestPrototypeAgent::test_prototype_agent_capabilities", "tests/test_specialized_agents.py::TestTextAgent::test_create_text_action", "tests/test_specialized_agents.py::TestTextAgent::test_text_agent_capabilities"]