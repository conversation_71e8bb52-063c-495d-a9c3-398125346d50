#!/usr/bin/env python3
"""
Test script for Claude Talk to Figma MCP Integration.
Tests the connection and functionality with your existing Figma MCP server.
"""

import sys
import os
import asyncio
import httpx
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Set test environment variables
os.environ["GEMINI_API_KEY"] = "test-key"
os.environ["FIGMA_ACCESS_TOKEN"] = "test-token"

async def test_mcp_server_connection():
    """Test connection to Claude Talk to Figma MCP server."""
    print("🔌 Testing Claude Talk to Figma MCP Server Connection...")
    
    server_url = "http://localhost:3055"
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test basic connectivity
            response = await client.get(f"{server_url}/health")
            
            if response.status_code == 200:
                print(f"✅ MCP server is running on {server_url}")
                print(f"   Status: {response.status_code}")
                return True
            else:
                print(f"⚠️  MCP server responded with status: {response.status_code}")
                return False
                
    except httpx.ConnectError:
        print(f"❌ Cannot connect to MCP server at {server_url}")
        print(f"   Make sure your Claude Talk to Figma MCP server is running on port 3055")
        return False
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

async def test_mcp_tools_list():
    """Test listing available MCP tools."""
    print("\n🛠️  Testing MCP Tools Discovery...")
    
    server_url = "http://localhost:3055"
    
    try:
        async with httpx.AsyncClient(timeout=15.0) as client:
            # Test tools/list endpoint
            mcp_request = {
                "jsonrpc": "2.0",
                "id": "test_tools_list",
                "method": "tools/list",
                "params": {}
            }
            
            response = await client.post(
                f"{server_url}/mcp",
                json=mcp_request,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if "result" in result and "tools" in result["result"]:
                    tools = result["result"]["tools"]
                    print(f"✅ Found {len(tools)} MCP tools:")
                    
                    for tool in tools:
                        name = tool.get("name", "Unknown")
                        description = tool.get("description", "No description")
                        print(f"   • {name}: {description}")
                    
                    return tools
                else:
                    print(f"⚠️  Unexpected response format: {result}")
                    return []
            else:
                print(f"❌ Tools list request failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return []
                
    except Exception as e:
        print(f"❌ Tools discovery failed: {e}")
        return []

async def test_figma_mcp_client():
    """Test Figma MCP client with Claude Talk to Figma configuration."""
    print("\n🤖 Testing Figma MCP Client Integration...")
    
    try:
        from tools.figma_mcp_tools import get_claude_talk_to_figma_client
        
        # Create client configured for Claude Talk to Figma
        client = get_claude_talk_to_figma_client()
        
        print(f"✅ Figma MCP client created")
        print(f"   Server URL: {client.config.server_url}")
        print(f"   Timeout: {client.config.default_timeout}s")
        print(f"   Max retries: {client.config.max_retries}")
        
        # List available tools
        tools = client.list_available_tools()
        print(f"   Available tools: {len(tools)}")
        for tool in tools:
            print(f"     - {tool}")
        
        # Test a simple tool execution (if tools are available)
        if tools:
            print(f"\n🚀 Testing tool execution...")
            
            # Try to execute a simple tool
            test_tool = tools[0]  # Use first available tool
            
            try:
                response = await client.execute_tool(
                    test_tool,
                    {"test": True}  # Simple test parameters
                )
                
                print(f"✅ Tool execution successful:")
                print(f"   Tool: {test_tool}")
                print(f"   Status: {response.status}")
                print(f"   Result: {response.result}")
                print(f"   Execution time: {response.execution_time}s")
                
            except Exception as tool_error:
                print(f"⚠️  Tool execution failed (expected for test): {tool_error}")
        
        await client.close()
        return True
        
    except Exception as e:
        print(f"❌ Figma MCP client test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_agent_integration():
    """Test agent integration with Claude Talk to Figma."""
    print("\n🎯 Testing Agent Integration...")
    
    try:
        from agents.design_agent import DesignAgent
        
        # Create design agent (will use Claude Talk to Figma by default)
        agent = DesignAgent()
        
        print(f"✅ Design agent created")
        print(f"   Agent name: {agent.name}")
        print(f"   Capabilities: {agent.capabilities}")
        print(f"   Figma MCP client configured: {agent.figma_mcp_client is not None}")
        
        if agent.figma_mcp_client:
            print(f"   MCP server URL: {agent.figma_mcp_client.config.server_url}")
            
            # List tools available to the agent
            figma_tools = agent.figma_mcp_client.list_available_tools()
            print(f"   Figma MCP tools: {len(figma_tools)}")
            for tool in figma_tools:
                print(f"     - {tool}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def print_setup_instructions():
    """Print setup instructions for Claude Talk to Figma integration."""
    print("\n" + "=" * 70)
    print("📚 CLAUDE TALK TO FIGMA INTEGRATION SETUP")
    print("=" * 70)
    
    print("""
🎯 INTEGRATION STATUS:

✅ Figma Agent System is configured to use your Claude Talk to Figma MCP server
✅ HTTP and WebSocket communication protocols are implemented
✅ Default configuration points to localhost:3055
✅ Agents will automatically use your Figma MCP tools

🔧 CONFIGURATION DETAILS:

Server URL: http://localhost:3055
Communication: HTTP with WebSocket fallback
Timeout: 45 seconds
Retries: 2 attempts

🚀 NEXT STEPS:

1. **Ensure your Claude Talk to Figma MCP server is running**:
   - Check that the server is active on port 3055
   - Verify the Figma plugin connection is established

2. **Test the integration**:
   - Run this test script to verify connectivity
   - Check that MCP tools are discoverable
   - Test agent execution with real Figma operations

3. **Start using the agents**:
   - The design_agent, color_agent, etc. will now use your MCP tools
   - No additional configuration needed
   - Tools will be called via HTTP/WebSocket to your server

4. **Monitor the integration**:
   - Check server logs for MCP tool calls
   - Verify Figma operations are executed correctly
   - Debug any connectivity or tool execution issues

💡 TROUBLESHOOTING:

- If connection fails: Ensure MCP server is running on port 3055
- If tools not found: Check MCP server tool registration
- If execution fails: Verify Figma access token and permissions
- For WebSocket issues: Check server WebSocket endpoint configuration

🎉 Your Figma Agent System is ready to use your Claude Talk to Figma MCP tools!
""")

async def main():
    """Run all Claude Talk to Figma integration tests."""
    print("🚀 Starting Claude Talk to Figma MCP Integration Tests...")
    print("=" * 70)
    
    success = True
    
    # Test MCP server connection
    if not await test_mcp_server_connection():
        success = False
        print("\n⚠️  MCP server connection failed. Make sure your server is running.")
    
    # Test MCP tools discovery
    tools = await test_mcp_tools_list()
    if not tools:
        print("\n⚠️  No MCP tools discovered. Check server configuration.")
    
    # Test Figma MCP client
    if not await test_figma_mcp_client():
        success = False
    
    # Test agent integration
    if not await test_agent_integration():
        success = False
    
    # Print setup instructions
    print_setup_instructions()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Claude Talk to Figma integration is ready!")
        print("\n💡 Your agents can now use your existing Figma MCP tools.")
    else:
        print("❌ Some integration tests failed.")
        print("\n🔧 Check the errors above and ensure your MCP server is running.")
    
    print("\n🚀 To start using the integrated system:")
    print("1. Ensure Claude Talk to Figma MCP server is running on port 3055")
    print("2. Start the FastAPI server: uvicorn api.fastapi_server:app --reload")
    print("3. Send requests to the agents - they'll use your MCP tools automatically!")

if __name__ == "__main__":
    asyncio.run(main())
