"""
Configuration for Figma MCP Tools Integration.
Customize this file to match your specific Figma MCP tools setup.
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from tools.figma_mcp_tools import FigmaMCPConfig


class FigmaMCPConfigurator:
    """
    Configurator for Figma MCP tools integration.
    Helps set up and customize the integration based on your specific tools.
    """
    
    def __init__(self):
        self.base_config = FigmaMCPConfig()
    
    def create_config_for_mcp_server(
        self, 
        server_command: str, 
        server_args: List[str] = None,
        figma_token: str = None
    ) -> FigmaMCPConfig:
        """
        Create configuration for MCP server-based tools.
        
        Args:
            server_command: Command to run your MCP server (e.g., 'node', 'python', 'npx')
            server_args: Arguments for the server command (e.g., ['mcp-server.js'])
            figma_token: Figma access token (optional, uses env var if not provided)
        
        Returns:
            FigmaMCPConfig configured for MCP server
        """
        return FigmaMCPConfig(
            server_command=server_command,
            server_args=server_args or [],
            figma_access_token=figma_token,
            default_timeout=60.0,  # Longer timeout for server operations
            max_retries=2
        )
    
    def create_config_for_direct_tools(
        self, 
        figma_token: str = None
    ) -> FigmaMCPConfig:
        """
        Create configuration for direct tool integration.
        
        Args:
            figma_token: Figma access token (optional, uses env var if not provided)
        
        Returns:
            FigmaMCPConfig configured for direct tools
        """
        return FigmaMCPConfig(
            server_command=None,  # No server command for direct tools
            server_args=[],
            figma_access_token=figma_token,
            default_timeout=30.0,
            max_retries=3
        )
    
    def detect_figma_mcp_setup(self) -> Dict[str, Any]:
        """
        Attempt to detect existing Figma MCP tools setup.
        
        Returns:
            Dictionary with detected configuration information
        """
        detection_result = {
            "mcp_server_found": False,
            "server_type": None,
            "server_path": None,
            "python_tools_found": False,
            "node_tools_found": False,
            "recommendations": []
        }
        
        # Check for common MCP server files
        common_server_files = [
            "mcp-server.js",
            "mcp-server.py", 
            "figma-mcp-server.js",
            "figma-mcp-server.py",
            "server.js",
            "server.py"
        ]
        
        current_dir = Path.cwd()
        parent_dirs = [current_dir, current_dir.parent, current_dir.parent.parent]
        
        for directory in parent_dirs:
            for server_file in common_server_files:
                server_path = directory / server_file
                if server_path.exists():
                    detection_result["mcp_server_found"] = True
                    detection_result["server_path"] = str(server_path)
                    detection_result["server_type"] = "node" if server_file.endswith(".js") else "python"
                    break
            
            if detection_result["mcp_server_found"]:
                break
        
        # Check for package.json (Node.js MCP tools)
        for directory in parent_dirs:
            package_json = directory / "package.json"
            if package_json.exists():
                detection_result["node_tools_found"] = True
                break
        
        # Check for Python MCP tools
        for directory in parent_dirs:
            if any((directory / f).exists() for f in ["requirements.txt", "pyproject.toml", "setup.py"]):
                detection_result["python_tools_found"] = True
                break
        
        # Generate recommendations
        if detection_result["mcp_server_found"]:
            server_type = detection_result["server_type"]
            server_path = detection_result["server_path"]
            
            if server_type == "node":
                detection_result["recommendations"].append(
                    f"Found Node.js MCP server at {server_path}. "
                    f"Use: create_config_for_mcp_server('node', ['{server_path}'])"
                )
            else:
                detection_result["recommendations"].append(
                    f"Found Python MCP server at {server_path}. "
                    f"Use: create_config_for_mcp_server('python', ['{server_path}'])"
                )
        
        elif detection_result["node_tools_found"]:
            detection_result["recommendations"].append(
                "Found Node.js project. If you have Figma MCP tools as npm package, "
                "use: create_config_for_mcp_server('npx', ['your-figma-mcp-package'])"
            )
        
        elif detection_result["python_tools_found"]:
            detection_result["recommendations"].append(
                "Found Python project. If you have Figma MCP tools as Python package, "
                "consider using direct tool integration with create_config_for_direct_tools()"
            )
        
        else:
            detection_result["recommendations"].append(
                "No MCP tools detected. Please specify your Figma MCP tools setup manually."
            )
        
        return detection_result


# Pre-configured setups for common scenarios
COMMON_CONFIGS = {
    "framelink_figma_mcp": FigmaMCPConfig(
        server_command="npx",
        server_args=["@framelink/figma-mcp"],
        default_timeout=45.0,
        max_retries=2
    ),

    "figma_mcp_server_bun": FigmaMCPConfig(
        server_command="bun",
        server_args=["C:/Users/<USER>/Desktop/claudetalktofigma/claude-talk-to-figma-mcp/src/talk_to_figma_mcp/server.ts"],
        default_timeout=30.0,
        max_retries=3
    ),

    "figma_mcp_server_python": FigmaMCPConfig(
        server_command="python",
        server_args=["figma_mcp_server.py"],
        default_timeout=30.0,
        max_retries=3
    ),

    "figma_mcp_server_bun": FigmaMCPConfig(
        server_command="bun",
        server_args=["C:/Users/<USER>/Desktop/claudetalktofigma/claude-talk-to-figma-mcp/src/talk_to_figma_mcp/server.ts"],
        default_timeout=30.0,
        max_retries=3
    ),

    "claude_talk_to_figma": FigmaMCPConfig(
        server_command="bun",
        server_args=["C:/Users/<USER>/Desktop/claudetalktofigma/claude-talk-to-figma-mcp/src/talk_to_figma_mcp/server.ts"],
        default_timeout=45.0,
        max_retries=2
    ),

    "direct_tools": FigmaMCPConfig(
        server_command=None,
        server_args=[],
        default_timeout=20.0,
        max_retries=3
    )
}


def get_config(config_name: str) -> Optional[FigmaMCPConfig]:
    """
    Get a pre-configured setup by name.
    
    Args:
        config_name: Name of the configuration
    
    Returns:
        FigmaMCPConfig or None if not found
    """
    return COMMON_CONFIGS.get(config_name)


def create_custom_config(
    server_command: str = None,
    server_args: List[str] = None,
    figma_token: str = None,
    timeout: float = 30.0,
    max_retries: int = 3
) -> FigmaMCPConfig:
    """
    Create a custom Figma MCP configuration.
    
    Args:
        server_command: Command to run MCP server (None for direct tools)
        server_args: Arguments for server command
        figma_token: Figma access token
        timeout: Default timeout for tool execution
        max_retries: Maximum retry attempts
    
    Returns:
        Custom FigmaMCPConfig
    """
    return FigmaMCPConfig(
        server_command=server_command,
        server_args=server_args or [],
        figma_access_token=figma_token,
        default_timeout=timeout,
        max_retries=max_retries
    )


# Example usage and setup instructions
SETUP_EXAMPLES = {
    "node_mcp_server": """
# For Node.js MCP Server
from config.figma_mcp_config import create_custom_config

config = create_custom_config(bun
    server_args=["C:/Users/<USER>/Desktop/claudetalktofigma/claude-talk-to-figma-mcp/src/talk_to_figma_mcp/server.ts"],
    timeout=45.0
)
""",
    
    "python_mcp_server": """
# For Python MCP Server  
from config.figma_mcp_config import create_custom_config

config = create_custom_config(
    server_command="python",
    server_args=["path/to/your/figma_mcp_server.py"],
    timeout=45.0
)
""",
    
    "npm_package": """
# For NPM Package MCP Tools
from config.figma_mcp_config import create_custom_config

config = create_custom_config(
    server_command="npx",
    server_args=["your-figma-mcp-package"],
    timeout=60.0
)
""",
    
    "direct_integration": """
# For Direct Tool Integration
from config.figma_mcp_config import create_custom_config

config = create_custom_config(
    server_command=None,  # No server needed
    timeout=30.0
)
"""
}


def print_setup_help():
    """Print help information for setting up Figma MCP tools."""
    print("🔧 Figma MCP Tools Setup Help")
    print("=" * 50)
    print()
    
    configurator = FigmaMCPConfigurator()
    detection = configurator.detect_figma_mcp_setup()
    
    print("📋 Detection Results:")
    print(f"  MCP Server Found: {detection['mcp_server_found']}")
    print(f"  Server Type: {detection['server_type']}")
    print(f"  Server Path: {detection['server_path']}")
    print(f"  Node.js Tools: {detection['node_tools_found']}")
    print(f"  Python Tools: {detection['python_tools_found']}")
    print()
    
    print("💡 Recommendations:")
    for rec in detection['recommendations']:
        print(f"  • {rec}")
    print()
    
    print("📚 Setup Examples:")
    for name, example in SETUP_EXAMPLES.items():
        print(f"\n{name.upper()}:")
        print(example)


if __name__ == "__main__":
    print_setup_help()
