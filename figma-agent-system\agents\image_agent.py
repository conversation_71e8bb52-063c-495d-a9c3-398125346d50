"""
Image/Icon Agent for Figma Agent System.
Handles image imports, icon management, and visual asset operations.
"""

import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentRequest, AgentResponse, AgentStatus, AgentCapability
from config.agent_activities import FIGMA_ACTIVITIES
from tools.mcp_integration import MC<PERSON><PERSON>, ToolRegistry, MCPTool
from tools.figma_api import FigmaAPIClient
from llm.gemini_client import Gemini<PERSON>lient
from utils.logging_config import create_agent_logger


class ImageAgent(BaseAgent):
    """
    Image/Icon Agent specializing in visual assets and media management.
    
    Capabilities:
    - Image import and placement
    - Icon creation and management
    - Asset optimization
    - Visual content generation
    - Media library organization
    """
    
    def __init__(
        self,
        mcp_client: MCPClient = None,
        figma_client: FigmaAPIClient = None,
        gemini_client: GeminiClient = None
    ):
        super().__init__(
            name="image_agent",
            capabilities=[AgentCapability.IMAGE]
        )
        
        # Initialize clients
        self.mcp_client = mcp_client or MCPClient()
        self.figma_client = figma_client or FigmaAPIClient()
        self.gemini_client = gemini_client or GeminiClient()
        
        # Initialize tool registry
        self.tool_registry = ToolRegistry(self.mcp_client)
        self._register_image_tools()
        
        # Agent-specific logger
        self.logger = create_agent_logger("image")
        
        # Image-specific metrics
        self.images_imported = 0
        self.icons_created = 0
        self.assets_optimized = 0
        
        self.logger.info("Image Agent initialized")

    def get_supported_actions(self) -> list:
        """Get list of actions this agent can handle."""
        return [
            "upload_image", "create_icon", "replace_image", "crop_image",
            "resize_image", "apply_image_effects", "create_mask",
            "generate_placeholder", "optimize_image", "create_image_fill",
            "extract_image_colors", "create_pattern_fill"
        ]

    def _can_handle_action(self, action: str) -> bool:
        """Check if this agent can handle the given action."""
        return action in self.get_supported_actions()

    def _register_image_tools(self) -> None:
        """Register image-related tools."""
        
        # Import image tool
        import_image_tool = MCPTool(
            name="import_image",
            description="Import an image into the design",
            parameters={
                "image_url": {"type": str, "required": True, "description": "Image URL or path"},
                "x": {"type": float, "required": True, "description": "X position"},
                "y": {"type": float, "required": True, "description": "Y position"},
                "width": {"type": float, "required": False, "description": "Image width"},
                "height": {"type": float, "required": False, "description": "Image height"}
            },
            handler=self._handle_import_image,
            timeout=20.0,
            max_retries=2
        )
        self.tool_registry.register_tool(import_image_tool, "image_management")
        
        # Create icon tool
        create_icon_tool = MCPTool(
            name="create_icon",
            description="Create an icon from vector shapes",
            parameters={
                "icon_type": {"type": str, "required": True, "description": "Type of icon to create"},
                "size": {"type": float, "required": False, "default": 24, "description": "Icon size"},
                "color": {"type": dict, "required": False, "description": "Icon color"},
                "style": {"type": str, "required": False, "default": "outline", "description": "Icon style"}
            },
            handler=self._handle_create_icon,
            timeout=15.0,
            max_retries=2
        )
        self.tool_registry.register_tool(create_icon_tool, "image_management")
    
    async def execute_task(self, request: AgentRequest) -> AgentResponse:
        """Execute an image task."""
        self.logger.info(
            "Executing image task",
            task_id=request.task_id,
            action=request.action
        )
        
        try:
            if not self._validate_request(request):
                return AgentResponse(
                    task_id=request.task_id,
                    agent_id=self.agent_id,
                    status=AgentStatus.FAILED,
                    error="Invalid request parameters"
                )
            
            if request.action not in FIGMA_ACTIVITIES:
                return AgentResponse(
                    task_id=request.task_id,
                    agent_id=self.agent_id,
                    status=AgentStatus.FAILED,
                    error=f"Unsupported action: {request.action}"
                )
            
            activity = FIGMA_ACTIVITIES[request.action]
            
            if activity.agent_capability not in self.capabilities:
                return AgentResponse(
                    task_id=request.task_id,
                    agent_id=self.agent_id,
                    status=AgentStatus.FAILED,
                    error=f"Action '{request.action}' not supported by Image Agent"
                )
            
            result = await self._execute_action(request.action, request.parameters, request.context)
            self._update_task_metrics(request.action)
            
            return AgentResponse(
                task_id=request.task_id,
                agent_name=self.name,
                status=AgentStatus.COMPLETED,
                result=result,
                execution_time=result.get("execution_time", 0.0),
                metadata={
                    "action": request.action,
                    "tools_used": result.get("tools_used", []),
                    "ai_assistance": result.get("ai_assistance", False)
                }
            )
            
        except Exception as e:
            self.logger.error(
                "Image task execution failed",
                task_id=request.task_id,
                action=request.action,
                error=str(e)
            )
            
            return AgentResponse(
                task_id=request.task_id,
                agent_name=self.name,
                status=AgentStatus.FAILED,
                error=str(e),
                execution_time=0.0
            )
    
    async def _execute_action(
        self,
        action: str,
        parameters: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute a specific image action."""
        
        action_tool_map = {
            "import_image": "import_image",
            "create_icon": "create_icon"
        }
        
        if action in action_tool_map:
            tool_name = action_tool_map[action]
            response = await self.tool_registry.execute_tool(tool_name, parameters, context)
            
            if response.status == "success":
                return {
                    "success": True,
                    "result": response.result,
                    "execution_time": response.execution_time,
                    "tools_used": [tool_name]
                }
            else:
                raise Exception(f"Tool execution failed: {response.error}")
        
        else:
            raise Exception(f"Unknown action: {action}")
    
    async def _handle_import_image(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle image import."""
        image_url = parameters.get("image_url")
        x = parameters.get("x")
        y = parameters.get("y")
        
        if not image_url or x is None or y is None:
            raise ValueError("image_url, x, and y are required")
        
        image_data = {
            "type": "RECTANGLE",  # Would be IMAGE in real implementation
            "x": x,
            "y": y,
            "width": parameters.get("width", 100),
            "height": parameters.get("height", 100),
            "fills": [{
                "type": "IMAGE",
                "imageRef": image_url,
                "scaleMode": "FILL"
            }],
            "imported_at": datetime.utcnow().isoformat()
        }
        
        image_id = f"img_{int(datetime.utcnow().timestamp())}"
        self.images_imported += 1
        
        return {
            "image_id": image_id,
            "image_data": image_data,
            "success": True
        }
    
    async def _handle_create_icon(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle icon creation."""
        icon_type = parameters.get("icon_type")
        if not icon_type:
            raise ValueError("icon_type is required")
        
        size = parameters.get("size", 24)
        color = parameters.get("color", {"r": 0, "g": 0, "b": 0, "a": 1})
        style = parameters.get("style", "outline")
        
        icon_data = {
            "type": "VECTOR",
            "name": f"{icon_type}_icon",
            "width": size,
            "height": size,
            "fills": [{"type": "SOLID", "color": color}],
            "icon_type": icon_type,
            "style": style,
            "created_at": datetime.utcnow().isoformat()
        }
        
        icon_id = f"icon_{int(datetime.utcnow().timestamp())}"
        self.icons_created += 1
        
        return {
            "icon_id": icon_id,
            "icon_data": icon_data,
            "success": True
        }
    
    def _validate_request(self, request: AgentRequest) -> bool:
        """Validate image request."""
        if not request.task_id or not request.action:
            return False
        
        if request.action == "import_image":
            params = request.parameters
            if not params.get("image_url") or params.get("x") is None or params.get("y") is None:
                return False
        
        return True
    
    def _update_task_metrics(self, action: str) -> None:
        """Update task-specific metrics."""
        if action == "import_image":
            self.images_imported += 1
        elif action == "create_icon":
            self.icons_created += 1
    
    def get_agent_metrics(self) -> Dict[str, Any]:
        """Get agent-specific metrics."""
        base_metrics = super().get_agent_metrics()
        
        image_metrics = {
            "images_imported": self.images_imported,
            "icons_created": self.icons_created,
            "assets_optimized": self.assets_optimized
        }
        
        return {**base_metrics, **image_metrics}
    
    async def close(self) -> None:
        """Close agent and cleanup resources."""
        await self.tool_registry.close()
        await self.figma_client.close()
        self.logger.info("Image Agent closed")
