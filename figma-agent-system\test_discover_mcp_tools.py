#!/usr/bin/env python3
"""
Discover Available MCP Tools from Claude Talk to Figma Server

This script connects to your Claude Talk to Figma MCP server and discovers
what tools are actually available for creating frames and other Figma operations.
"""

import asyncio
import json
import websockets
from datetime import datetime

# Configuration
WEBSOCKET_URL = "ws://localhost:3055"
CHANNEL_ID = "oa34ym6m"

async def discover_mcp_tools():
    """Discover what tools are available in the Claude Talk to Figma MCP server."""
    print(f"🔍 Discovering MCP Tools from Claude Talk to Figma Server")
    print(f"WebSocket URL: {WEBSOCKET_URL}")
    print(f"Channel: {CHANNEL_ID}")
    print("=" * 70)
    
    try:
        # Connect to WebSocket
        print(f"📡 Connecting to WebSocket...")
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            print(f"✅ Connected to {WEBSOCKET_URL}")
            
            # Wait for initial message
            initial_message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            print(f"📨 Initial message: {initial_message}")
            
            # Join channel
            join_message = {
                "type": "join",
                "channel": CHANNEL_ID
            }
            
            print(f"🔗 Joining channel: {CHANNEL_ID}")
            await websocket.send(json.dumps(join_message))
            
            # Wait for join confirmation
            join_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
            join_data = json.loads(join_response)
            print(f"✅ Join response: {join_data}")
            
            # Try to discover available tools
            print(f"\n🛠️  Discovering available tools...")
            
            # Method 1: Try to list tools (common MCP pattern)
            tools_request = {
                "type": "list_tools",
                "id": f"list_tools_{datetime.now().timestamp()}"
            }
            
            print(f"📤 Sending list_tools request...")
            await websocket.send(json.dumps(tools_request))
            
            try:
                tools_response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                tools_data = json.loads(tools_response)
                print(f"✅ Tools response: {json.dumps(tools_data, indent=2)}")
                
                if "tools" in tools_data:
                    print(f"\n📋 Available Tools:")
                    for tool in tools_data["tools"]:
                        print(f"   - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
                
            except asyncio.TimeoutError:
                print(f"⏰ No response to list_tools request")
            
            # Method 2: Try common tool names to see what's available
            print(f"\n🧪 Testing common tool names...")
            
            common_tools = [
                "get_figma_data",
                "download_figma_images", 
                "create_frame",
                "create_node",
                "add_frame",
                "insert_frame",
                "figma_create_frame",
                "figma_add_node",
                "create_rectangle",
                "add_rectangle",
                "figma_create_rectangle",
                "list_tools",
                "help",
                "capabilities"
            ]
            
            available_tools = []
            
            for tool_name in common_tools:
                try:
                    # Test tool call
                    test_request = {
                        "type": "tool_call",
                        "tool": tool_name,
                        "parameters": {},
                        "id": f"{tool_name}_{datetime.now().timestamp()}"
                    }
                    
                    print(f"   Testing: {tool_name}...", end="")
                    await websocket.send(json.dumps(test_request))
                    
                    # Wait for response
                    response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    response_data = json.loads(response)
                    
                    if response_data.get("type") == "error":
                        error_msg = response_data.get("message", "Unknown error")
                        if "not found" in error_msg.lower() or "unknown" in error_msg.lower():
                            print(f" ❌ Not available")
                        else:
                            print(f" ⚠️  Available but error: {error_msg}")
                            available_tools.append(tool_name)
                    else:
                        print(f" ✅ Available")
                        available_tools.append(tool_name)
                        print(f"      Response: {json.dumps(response_data, indent=6)}")
                    
                except asyncio.TimeoutError:
                    print(f" ⏰ Timeout")
                except Exception as e:
                    print(f" ❌ Error: {e}")
                
                # Brief pause between tests
                await asyncio.sleep(0.5)
            
            # Summary
            print(f"\n📊 DISCOVERY SUMMARY")
            print(f"=" * 70)
            print(f"Available Tools Found: {len(available_tools)}")
            
            if available_tools:
                print(f"✅ Working Tools:")
                for tool in available_tools:
                    print(f"   - {tool}")
            else:
                print(f"❌ No tools found or all tools returned errors")
            
            # Method 3: Try to get help or documentation
            print(f"\n📚 Trying to get help/documentation...")
            
            help_requests = [
                {"type": "help"},
                {"type": "get_help"},
                {"type": "documentation"},
                {"type": "tool_call", "tool": "help", "parameters": {}},
                {"type": "tool_call", "tool": "list", "parameters": {}},
                {"type": "capabilities"}
            ]
            
            for help_req in help_requests:
                try:
                    print(f"   Trying: {help_req}...", end="")
                    await websocket.send(json.dumps(help_req))
                    
                    help_response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    help_data = json.loads(help_response)
                    
                    print(f" ✅ Response received")
                    print(f"      {json.dumps(help_data, indent=6)}")
                    
                except asyncio.TimeoutError:
                    print(f" ⏰ Timeout")
                except Exception as e:
                    print(f" ❌ Error: {e}")
                
                await asyncio.sleep(0.5)
            
            return available_tools
            
    except Exception as e:
        print(f"❌ Discovery failed: {e}")
        import traceback
        traceback.print_exc()
        return []

async def test_specific_figma_operations():
    """Test specific Figma operations that might create frames."""
    print(f"\n🎯 Testing Specific Figma Operations")
    print("=" * 70)
    
    try:
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            # Join channel
            join_message = {"type": "join", "channel": CHANNEL_ID}
            await websocket.send(json.dumps(join_message))
            await websocket.recv()  # Join confirmation
            
            # Test operations that might create frames
            figma_operations = [
                {
                    "name": "get_figma_data with file key",
                    "request": {
                        "type": "tool_call",
                        "tool": "get_figma_data",
                        "parameters": {
                            "fileKey": "test-file-key"
                        },
                        "id": "test_get_data"
                    }
                },
                {
                    "name": "Figma plugin command",
                    "request": {
                        "type": "figma_command",
                        "command": "create_frame",
                        "parameters": {
                            "width": 400,
                            "height": 300,
                            "name": "Test Frame"
                        }
                    }
                },
                {
                    "name": "Plugin API call",
                    "request": {
                        "type": "plugin_api",
                        "method": "createFrame",
                        "params": {
                            "width": 400,
                            "height": 300
                        }
                    }
                }
            ]
            
            for operation in figma_operations:
                try:
                    print(f"🧪 Testing: {operation['name']}")
                    await websocket.send(json.dumps(operation['request']))
                    
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    
                    print(f"   ✅ Response: {json.dumps(response_data, indent=4)}")
                    
                except asyncio.TimeoutError:
                    print(f"   ⏰ Timeout")
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                
                await asyncio.sleep(1)
                
    except Exception as e:
        print(f"❌ Figma operations test failed: {e}")

async def main():
    """Main discovery function."""
    print("🚀 Claude Talk to Figma MCP Tool Discovery")
    print(f"Timestamp: {datetime.now()}")
    print("=" * 80)
    
    # Discover available tools
    available_tools = await discover_mcp_tools()
    
    # Test specific Figma operations
    await test_specific_figma_operations()
    
    # Final summary
    print(f"\n🎯 FINAL SUMMARY")
    print("=" * 80)
    print(f"Channel: {CHANNEL_ID}")
    print(f"WebSocket: {WEBSOCKET_URL}")
    print(f"Available Tools: {len(available_tools)}")
    
    if available_tools:
        print(f"✅ Your Claude Talk to Figma server supports these tools:")
        for tool in available_tools:
            print(f"   - {tool}")
        
        print(f"\n💡 Next Steps:")
        print(f"1. Use these tools in your agents")
        print(f"2. Check if any of these can create frames")
        print(f"3. Look for documentation on frame creation")
    else:
        print(f"❌ No tools discovered")
        print(f"💡 Possible issues:")
        print(f"1. MCP server might use different protocol")
        print(f"2. Tools might require authentication")
        print(f"3. Server might be in different mode")
    
    print(f"\n🔧 Recommendation:")
    print(f"Check your Claude Talk to Figma server documentation")
    print(f"for the correct tool names and parameters for creating frames.")

if __name__ == "__main__":
    asyncio.run(main())
