#!/usr/bin/env python3
"""
Test Updated Design Agent with Direct WebSocket Communication

Test the design agent's new frame creation capability using direct WebSocket
communication with the Claude Talk to Figma plugin.
"""

import asyncio
import json
import requests
from datetime import datetime

# Configuration
ROUTER_URL = "http://localhost:8000"

async def test_frame_creation():
    """Test frame creation through the updated design agent."""
    print(f"🎯 Testing Updated Design Agent Frame Creation")
    print(f"Router URL: {ROUTER_URL}")
    print("=" * 70)
    
    # Test frame creation request
    frame_request = {
        "request_id": f"test_frame_{int(datetime.now().timestamp())}",
        "user_id": "test_user",
        "message": "Create a frame with width 400 and height 300 named 'Agent Test Frame'",
        "context": {
            "file_key": "test-file-key",  # This might not be needed for direct WebSocket
            "channel": "oa34ym6m"
        },
        "timestamp": datetime.now().isoformat()
    }
    
    print(f"📤 Sending frame creation request:")
    print(f"   Message: {frame_request['message']}")
    print(f"   Context: {frame_request['context']}")
    
    try:
        # Send request to router
        response = requests.post(
            f"{ROUTER_URL}/process",
            json=frame_request,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Router Response:")
            print(f"   Status: {result.get('status')}")
            print(f"   Agent: {result.get('agent_type')}")
            print(f"   Success: {result.get('success')}")
            print(f"   Message: {result.get('message')}")
            
            if result.get('data'):
                print(f"   Frame Data: {json.dumps(result['data'], indent=4)}")
            
            if result.get('success'):
                print(f"🎉 SUCCESS! Frame creation completed!")
                print(f"💡 Check your Figma file for the new frame: '{result.get('data', {}).get('frame_data', {}).get('name', 'Agent Test Frame')}'")
                return True
            else:
                print(f"❌ Frame creation failed: {result.get('message')}")
                return False
                
        else:
            print(f"❌ Router request failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

async def test_multiple_frames():
    """Test creating multiple frames with different parameters."""
    print(f"\n🖼️  Testing Multiple Frame Creation")
    print("=" * 70)
    
    frame_tests = [
        {
            "name": "Small Frame",
            "message": "Create a small frame 200x150 named 'Small Test Frame'",
            "expected_size": "200x150"
        },
        {
            "name": "Large Frame", 
            "message": "Create a large frame 800x600 named 'Large Test Frame'",
            "expected_size": "800x600"
        },
        {
            "name": "Positioned Frame",
            "message": "Create a frame 400x300 at position 100,200 named 'Positioned Frame'",
            "expected_position": "100,200"
        }
    ]
    
    successful_frames = []
    
    for i, test in enumerate(frame_tests):
        print(f"\n🧪 Test {i+1}: {test['name']}")
        
        frame_request = {
            "request_id": f"multi_frame_{i}_{int(datetime.now().timestamp())}",
            "user_id": "test_user",
            "message": test['message'],
            "context": {
                "file_key": "test-file-key",
                "channel": "oa34ym6m"
            },
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            response = requests.post(
                f"{ROUTER_URL}/process",
                json=frame_request,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('success'):
                    print(f"   ✅ {test['name']} created successfully!")
                    successful_frames.append(test['name'])
                else:
                    print(f"   ❌ {test['name']} failed: {result.get('message')}")
            else:
                print(f"   ❌ Request failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Wait between requests
        await asyncio.sleep(2)
    
    print(f"\n📊 Multiple Frame Test Results:")
    print(f"   Attempted: {len(frame_tests)}")
    print(f"   Successful: {len(successful_frames)}")
    
    if successful_frames:
        print(f"✅ Successfully created frames:")
        for frame in successful_frames:
            print(f"   - {frame}")
    
    return len(successful_frames)

async def test_router_health():
    """Test if the router is healthy and responsive."""
    print(f"🏥 Testing Router Health")
    print("=" * 70)
    
    try:
        response = requests.get(f"{ROUTER_URL}/health", timeout=10)
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Router is healthy:")
            print(f"   Status: {health_data.get('status')}")
            print(f"   Timestamp: {health_data.get('timestamp')}")
            return True
        else:
            print(f"❌ Router health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Router health check error: {e}")
        return False

async def main():
    """Main test function."""
    print("🚀 Updated Design Agent Testing")
    print(f"Timestamp: {datetime.now()}")
    print("=" * 80)
    
    # Test router health first
    router_healthy = await test_router_health()
    
    if not router_healthy:
        print(f"❌ Router is not healthy. Please start the FastAPI server first.")
        return
    
    # Test single frame creation
    single_frame_success = await test_frame_creation()
    
    # Test multiple frame creation
    successful_count = await test_multiple_frames()
    
    # Final summary
    print(f"\n🎯 FINAL TEST RESULTS")
    print("=" * 80)
    
    if single_frame_success or successful_count > 0:
        print(f"🎉 SUCCESS! The updated design agent is working!")
        print(f"✅ Single frame test: {'PASSED' if single_frame_success else 'FAILED'}")
        print(f"✅ Multiple frames test: {successful_count}/3 successful")
        
        print(f"\n💡 What's working:")
        print(f"✅ Router is processing requests")
        print(f"✅ Design agent is being selected")
        print(f"✅ WebSocket communication is implemented")
        print(f"✅ Frame creation commands are being sent")
        
        print(f"\n📋 Next Steps:")
        print(f"1. Check your Figma file for the created frames")
        print(f"2. If frames appear, the integration is fully working!")
        print(f"3. If no frames appear, the plugin might need manual activation")
        
    else:
        print(f"❌ All tests failed")
        print(f"💡 Possible issues:")
        print(f"1. Router might not be running")
        print(f"2. Design agent might have errors")
        print(f"3. WebSocket connection might be failing")
        print(f"4. Figma plugin might not be responding")
    
    print(f"\n🔍 Important:")
    print(f"Even if tests show success, please check your Figma file")
    print(f"to confirm that frames are actually being created!")

if __name__ == "__main__":
    asyncio.run(main())
