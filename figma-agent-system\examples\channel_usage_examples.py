#!/usr/bin/env python3
"""
Channel Usage Examples for Claude Talk to Figma Integration.

This file demonstrates how to properly configure and use channels
when working with the Figma Agent System and your Claude Talk to Figma MCP server.
"""

import asyncio
import j<PERSON>
from typing import Dict, Any

# Example 1: Basic Channel Configuration
async def example_basic_channel_usage():
    """Example of basic channel usage with your active Figma plugin channel."""
    print("📋 Example 1: Basic Channel Usage")
    
    from tools.figma_mcp_tools import get_claude_talk_to_figma_client
    
    # Create client with your active channel
    client = get_claude_talk_to_figma_client(channel_id="oa34ym6m")
    
    # Execute a tool with channel context
    context = {
        "agent_name": "design_agent",
        "channel_id": "oa34ym6m",  # Your active Figma plugin channel
        "task_id": "example-task-1"
    }
    
    try:
        response = await client.execute_tool(
            "get_figma_data",
            {"fileKey": "your-figma-file-key"},
            context
        )
        print(f"✅ Tool executed successfully on channel: {context['channel_id']}")
        print(f"   Status: {response.status}")
        
    except Exception as e:
        print(f"⚠️  Tool execution failed: {e}")
    
    await client.close()

# Example 2: Agent-Specific Channel Configuration
async def example_agent_specific_channels():
    """Example of configuring different channels for different agents."""
    print("\n📋 Example 2: Agent-Specific Channels")
    
    from tools.figma_mcp_tools import get_claude_talk_to_figma_client
    
    # Create client with agent-specific channels
    client = get_claude_talk_to_figma_client(
        channel_id="oa34ym6m",  # Default channel (your active one)
        channel_per_agent={
            "design_agent": "oa34ym6m",      # Your active channel for design
            "color_agent": "oa34ym6m",       # Same channel for color operations
            "text_agent": "oa34ym6m",        # Same channel for text operations
            # Add more agents as needed
        }
    )
    
    # Test different agent contexts
    test_agents = ["design_agent", "color_agent", "text_agent"]
    
    for agent_name in test_agents:
        context = {"agent_name": agent_name}
        channel = client._get_channel_for_context(context)
        print(f"   {agent_name}: {channel}")
    
    await client.close()

# Example 3: API Request with Channel Context
def example_api_request_with_channel():
    """Example of making API requests with channel context."""
    print("\n📋 Example 3: API Request with Channel Context")
    
    # Example curl command
    curl_command = '''
curl -X POST "http://localhost:8000/agents/design/execute" \\
  -H "Content-Type: application/json" \\
  -d '{
    "task_description": "Get information about the current Figma file",
    "parameters": {
      "fileKey": "your-figma-file-key",
      "nodeId": "optional-node-id"
    },
    "context": {
      "channel_id": "oa34ym6m",
      "agent_name": "design_agent",
      "task_priority": "high"
    }
  }'
'''
    
    print("   Use this curl command to test channel communication:")
    print(curl_command)
    
    # Example Python request
    python_request = '''
import requests

response = requests.post(
    "http://localhost:8000/agents/design/execute",
    json={
        "task_description": "Get Figma file information",
        "parameters": {
            "fileKey": "your-figma-file-key"
        },
        "context": {
            "channel_id": "oa34ym6m",  # Your active channel
            "agent_name": "design_agent"
        }
    }
)

print(f"Status: {response.status_code}")
print(f"Response: {response.json()}")
'''
    
    print("\n   Or use this Python code:")
    print(python_request)

# Example 4: Channel Testing and Validation
async def example_channel_testing():
    """Example of testing channel connectivity."""
    print("\n📋 Example 4: Channel Testing")
    
    from tools.figma_mcp_tools import get_claude_talk_to_figma_client
    
    # Create client
    client = get_claude_talk_to_figma_client(channel_id="oa34ym6m")
    
    # Test channel connection
    print("🔌 Testing channel connection...")
    result = await client.test_channel_connection("oa34ym6m")
    
    if result["success"]:
        print(f"✅ Channel connection successful!")
        print(f"   Channel: {result['channel']}")
        print(f"   System message: {result['system_message']['message']}")
        print(f"   Join response: {result['join_response']['message']}")
    else:
        print(f"❌ Channel connection failed!")
        print(f"   Error: {result['error']}")
    
    await client.close()

# Example 5: Custom Channel Configuration
async def example_custom_configuration():
    """Example of creating custom channel configurations."""
    print("\n📋 Example 5: Custom Channel Configuration")
    
    from tools.figma_mcp_tools import FigmaMCPConfig, FigmaMCPClient
    
    # Create custom configuration
    custom_config = FigmaMCPConfig(
        server_url="http://localhost:3055",
        server_port=3055,
        default_timeout=45.0,
        max_retries=2,
        default_channel="oa34ym6m",  # Your active channel
        channel_per_agent={
            "design_agent": "oa34ym6m",
            "color_agent": "oa34ym6m"
        },
        auto_join_channel=True,
        channel_timeout=15.0  # Custom timeout
    )
    
    # Create client with custom configuration
    client = FigmaMCPClient(custom_config)
    
    print(f"✅ Custom configuration created:")
    print(f"   Default channel: {custom_config.default_channel}")
    print(f"   Channel timeout: {custom_config.channel_timeout}s")
    print(f"   Agent channels: {custom_config.channel_per_agent}")
    
    await client.close()

# Example 6: Error Handling and Troubleshooting
async def example_error_handling():
    """Example of handling channel-related errors."""
    print("\n📋 Example 6: Error Handling")
    
    from tools.figma_mcp_tools import get_claude_talk_to_figma_client
    
    client = get_claude_talk_to_figma_client(channel_id="oa34ym6m")
    
    # Test with invalid channel (for demonstration)
    print("🧪 Testing error handling with invalid channel...")
    
    try:
        result = await client.test_channel_connection("invalid-channel")
        if not result["success"]:
            print(f"⚠️  Expected error: {result['error']}")
    except Exception as e:
        print(f"⚠️  Exception caught: {e}")
    
    # Test with correct channel
    print("🧪 Testing with correct channel...")
    try:
        result = await client.test_channel_connection("1re91tfd")
        if result["success"]:
            print(f"✅ Correct channel works: {result['channel']}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    
    await client.close()

# Example 7: Production Usage Pattern
async def example_production_usage():
    """Example of production-ready channel usage pattern."""
    print("\n📋 Example 7: Production Usage Pattern")
    
    from tools.figma_mcp_tools import get_claude_talk_to_figma_client
    
    # Production configuration
    client = get_claude_talk_to_figma_client(
        channel_id="oa34ym6m",  # Your active channel
        channel_per_agent={
            "design_agent": "oa34ym6m",
            "color_agent": "oa34ym6m",
            "text_agent": "oa34ym6m",
            "component_agent": "oa34ym6m",
            "image_agent": "oa34ym6m",
            "prototype_agent": "oa34ym6m",
            "export_agent": "oa34ym6m",
            "collaboration_agent": "oa34ym6m"
        }
    )
    
    # Production workflow
    agents_to_test = [
        "design_agent", "color_agent", "text_agent", 
        "component_agent", "image_agent"
    ]
    
    print("🚀 Production channel configuration:")
    for agent in agents_to_test:
        context = {"agent_name": agent}
        channel = client._get_channel_for_context(context)
        print(f"   {agent}: {channel}")
    
    # Test connectivity for production
    print("\n🔍 Testing production connectivity...")
    result = await client.test_channel_connection("1re91tfd")
    
    if result["success"]:
        print("✅ Production channel is ready!")
        print("   All agents configured to use your active Figma plugin channel")
    else:
        print("❌ Production channel test failed!")
        print(f"   Error: {result.get('error', 'Unknown error')}")
    
    await client.close()

async def main():
    """Run all channel usage examples."""
    print("🚀 Channel Usage Examples for Claude Talk to Figma Integration")
    print("=" * 80)
    print("These examples show how to properly configure and use channels")
    print("with your active Figma plugin channel: 1re91tfd")
    print("=" * 80)
    
    # Run examples
    await example_basic_channel_usage()
    await example_agent_specific_channels()
    example_api_request_with_channel()
    await example_channel_testing()
    await example_custom_configuration()
    await example_error_handling()
    await example_production_usage()
    
    print("\n" + "=" * 80)
    print("🎉 Channel Usage Examples Complete!")
    print("\n💡 Key Takeaways:")
    print("✅ Always use your active channel '1re91tfd' for communication")
    print("✅ Include channel context in agent requests")
    print("✅ Test channel connectivity before production use")
    print("✅ Configure all agents to use the same active channel")
    print("✅ Handle channel errors gracefully")
    print("\n🚀 Your Figma Agent System is ready to use channels properly!")

if __name__ == "__main__":
    asyncio.run(main())
