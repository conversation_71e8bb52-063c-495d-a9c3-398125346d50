"""
Unit tests for BaseAgent class.
Tests core agent functionality and abstract methods.
"""

import pytest
import pytest_asyncio
import os
from unittest.mock import Mock, AsyncMock

# Set test environment variables
os.environ["GEMINI_API_KEY"] = "test-key"
os.environ["FIGMA_ACCESS_TOKEN"] = "test-token"
os.environ["SECRET_KEY"] = "test-secret-key"

from agents.base_agent import BaseAgent, AgentRequest, AgentResponse, AgentCapability, AgentStatus


class TestAgent(BaseAgent):
    """Test implementation of BaseAgent for unit testing."""
    
    def __init__(self):
        super().__init__(
            name="test_agent",
            capabilities=[AgentCapability.DESIGN, AgentCapability.TEXT]
        )
        
    def get_supported_actions(self) -> list:
        """Get supported actions."""
        return ["create_frame", "add_text", "test_action"]
    
    def _can_handle_action(self, action: str) -> bool:
        """Check if agent can handle action."""
        return action in self.get_supported_actions()
    
    async def _execute_action(self, request: AgentRequest) -> dict:
        """Execute specific action."""
        action = request.action
        parameters = request.parameters

        if action == "create_frame":
            return {"frame_id": "test_frame_123", "width": parameters.get("width", 800)}
        elif action == "add_text":
            return {"text_id": "test_text_456", "content": parameters.get("text", "Hello World")}
        elif action == "test_action":
            return {"result": "success", "parameters": parameters}
        else:
            raise ValueError(f"Unsupported action: {action}")


class TestBaseAgent:
    """Test suite for BaseAgent functionality."""
    
    @pytest_asyncio.fixture
    async def test_agent(self):
        """Create test agent instance."""
        agent = TestAgent()
        await agent.initialize()
        return agent
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, test_agent):
        """Test agent initialization."""
        assert test_agent.name == "test_agent"
        assert AgentCapability.DESIGN in test_agent.capabilities
        assert AgentCapability.TEXT in test_agent.capabilities
        assert test_agent.status == AgentStatus.IDLE
        assert test_agent.is_available
        assert test_agent.max_concurrent_tasks == 3
        assert test_agent.default_timeout == 60
    
    @pytest.mark.asyncio
    async def test_supported_actions(self, test_agent):
        """Test supported actions."""
        actions = test_agent.get_supported_actions()
        assert "create_frame" in actions
        assert "add_text" in actions
        assert "test_action" in actions
        assert len(actions) == 3
    
    @pytest.mark.asyncio
    async def test_can_handle_action(self, test_agent):
        """Test action handling capability."""
        assert test_agent._can_handle_action("create_frame")
        assert test_agent._can_handle_action("add_text")
        assert test_agent._can_handle_action("test_action")
        assert not test_agent._can_handle_action("unsupported_action")
    
    @pytest.mark.asyncio
    async def test_execute_action(self, test_agent):
        """Test action execution."""
        # Test create_frame action
        request = AgentRequest(task_id="test", action="create_frame", parameters={"width": 1200})
        result = await test_agent._execute_action(request)
        assert result["frame_id"] == "test_frame_123"
        assert result["width"] == 1200

        # Test add_text action
        request = AgentRequest(task_id="test", action="add_text", parameters={"text": "Custom Text"})
        result = await test_agent._execute_action(request)
        assert result["text_id"] == "test_text_456"
        assert result["content"] == "Custom Text"

        # Test test_action
        request = AgentRequest(task_id="test", action="test_action", parameters={"param1": "value1"})
        result = await test_agent._execute_action(request)
        assert result["result"] == "success"
        assert result["parameters"]["param1"] == "value1"
    
    @pytest.mark.asyncio
    async def test_execute_task(self, test_agent):
        """Test task execution through main interface."""
        request = AgentRequest(
            task_id="test_task_001",
            action="create_frame",
            parameters={"width": 1000, "height": 600},
            context={"user_id": "test_user"}
        )
        
        response = await test_agent.execute_task(request)
        
        assert isinstance(response, AgentResponse)
        assert response.task_id == "test_task_001"
        assert response.agent_name == "test_agent"
        assert response.status == AgentStatus.COMPLETED
        assert response.result["frame_id"] == "test_frame_123"
        assert response.result["width"] == 1000
        assert response.execution_time >= 0
    
    @pytest.mark.asyncio
    async def test_execute_unsupported_action(self, test_agent):
        """Test execution of unsupported action."""
        request = AgentRequest(
            task_id="test_task_002",
            action="unsupported_action",
            parameters={}
        )

        response = await test_agent.execute_task(request)

        assert response.status == AgentStatus.FAILED
        assert "cannot handle action" in response.error.lower()
    
    @pytest.mark.asyncio
    async def test_performance_metrics(self, test_agent):
        """Test performance metrics tracking."""
        initial_metrics = test_agent.performance_metrics
        assert initial_metrics["total_tasks"] == 0
        assert initial_metrics["successful_tasks"] == 0
        assert initial_metrics["failed_tasks"] == 0
        assert initial_metrics["success_rate"] == 0.0
        
        # Execute successful task
        request = AgentRequest(
            task_id="metrics_test_001",
            action="test_action",
            parameters={"test": "value"}
        )
        
        response = await test_agent.execute_task(request)

        # Ensure task completed successfully
        assert response.status == AgentStatus.COMPLETED

        metrics = test_agent.performance_metrics
        assert metrics["total_tasks"] == 1
        assert metrics["successful_tasks"] == 1
        assert metrics["failed_tasks"] == 0
        assert metrics["success_rate"] == 1.0
        assert metrics["average_execution_time"] >= 0
    
    @pytest.mark.asyncio
    async def test_concurrent_task_limit(self, test_agent):
        """Test concurrent task limit."""
        assert test_agent.current_load == 0.0
        assert test_agent.is_available
        
        # Simulate adding tasks to current tasks
        test_agent._current_tasks["task1"] = Mock()
        test_agent._current_tasks["task2"] = Mock()
        
        assert test_agent.current_load == 2/3  # 2 out of 3 max tasks
        assert test_agent.is_available  # Still available
        
        # Add third task
        test_agent._current_tasks["task3"] = Mock()
        
        assert test_agent.current_load == 1.0  # At capacity
        assert not test_agent.is_available  # No longer available
    
    @pytest.mark.asyncio
    async def test_agent_status_transitions(self, test_agent):
        """Test agent status transitions."""
        assert test_agent.status == AgentStatus.IDLE

        # Simulate running status (still available if under capacity)
        test_agent._status = AgentStatus.RUNNING
        assert test_agent.status == AgentStatus.RUNNING
        assert test_agent.is_available  # Still available if under capacity

        # Simulate failed status (not available)
        test_agent._status = AgentStatus.FAILED
        assert test_agent.status == AgentStatus.FAILED
        assert not test_agent.is_available

        # Back to idle
        test_agent._status = AgentStatus.IDLE
        assert test_agent.status == AgentStatus.IDLE
        assert test_agent.is_available
    
    @pytest.mark.asyncio
    async def test_task_history(self, test_agent):
        """Test task history tracking."""
        assert len(test_agent._task_history) == 0
        
        request = AgentRequest(
            task_id="history_test_001",
            action="test_action",
            parameters={}
        )
        
        response = await test_agent.execute_task(request)
        
        # Check that response is added to history
        assert len(test_agent._task_history) == 1
        assert test_agent._task_history[0].task_id == "history_test_001"
    
    @pytest.mark.asyncio
    async def test_agent_cleanup(self, test_agent):
        """Test agent cleanup."""
        assert test_agent.status == AgentStatus.IDLE

        # Simulate cleanup by setting status to cancelled
        test_agent._status = AgentStatus.CANCELLED

        # Agent should be in cancelled state after cleanup
        assert test_agent.status == AgentStatus.CANCELLED


@pytest.mark.asyncio
async def test_agent_request_validation():
    """Test AgentRequest model validation."""
    # Valid request
    request = AgentRequest(
        task_id="test_001",
        action="test_action",
        parameters={"key": "value"}
    )
    
    assert request.task_id == "test_001"
    assert request.action == "test_action"
    assert request.parameters["key"] == "value"
    assert request.context is None
    assert request.timeout is None


@pytest.mark.asyncio
async def test_agent_response_validation():
    """Test AgentResponse model validation."""
    response = AgentResponse(
        task_id="test_001",
        agent_name="test_agent",
        status=AgentStatus.COMPLETED,
        result={"success": True},
        execution_time=1.5
    )
    
    assert response.task_id == "test_001"
    assert response.agent_name == "test_agent"
    assert response.status == AgentStatus.COMPLETED
    assert response.result["success"] is True
    assert response.execution_time == 1.5
    assert response.error is None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
