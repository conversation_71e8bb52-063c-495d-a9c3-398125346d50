"""
Unit tests for specialized agent implementations.
Tests individual agent capabilities and functionality.
"""

import pytest
import pytest_asyncio
import os
from unittest.mock import Mock, AsyncMock, patch

# Set test environment variables
os.environ["GEMINI_API_KEY"] = "test-key"
os.environ["FIGMA_ACCESS_TOKEN"] = "test-token"
os.environ["SECRET_KEY"] = "test-secret-key"

from agents.design_agent import DesignAgent
from agents.component_agent import ComponentAgent
from agents.text_agent import TextAgent
from agents.color_agent import ColorAgent
from agents.image_agent import ImageAgent
from agents.prototype_agent import PrototypeAgent
from agents.export_agent import ExportAgent
from agents.collaboration_agent import CollaborationAgent
from agents.base_agent import AgentRequest, AgentResponse, AgentCapability, AgentStatus


class TestDesignAgent:
    """Test suite for DesignAgent."""
    
    @pytest_asyncio.fixture
    async def design_agent(self):
        """Create design agent instance."""
        with patch('agents.design_agent.GeminiClient') as mock_gemini, \
             patch('agents.design_agent.MCPClient') as mock_mcp:
            
            mock_gemini_instance = AsyncMock()
            mock_gemini.return_value = mock_gemini_instance
            
            mock_mcp_instance = AsyncMock()
            mock_mcp.return_value = mock_mcp_instance
            
            agent = DesignAgent()
            await agent.initialize()
            return agent
    
    @pytest.mark.asyncio
    async def test_design_agent_capabilities(self, design_agent):
        """Test design agent capabilities."""
        assert AgentCapability.DESIGN in design_agent.capabilities
        assert design_agent.name == "design_agent"
        
        actions = design_agent.get_supported_actions()
        expected_actions = [
            "create_frame", "create_component", "create_layout",
            "apply_design_system", "generate_design_suggestions",
            "create_wireframe", "apply_theme", "create_responsive_layout",
            "optimize_layout", "create_grid_system"
        ]

        for action in expected_actions:
            assert action in actions
    
    @pytest.mark.asyncio
    async def test_create_frame_action(self, design_agent):
        """Test create frame action."""
        request = AgentRequest(
            task_id="design_test_001",
            action="create_frame",
            parameters={"width": 800, "height": 600, "name": "Test Frame"}
        )
        
        with patch.object(design_agent, '_execute_action') as mock_execute:
            mock_execute.return_value = {"frame_id": "123:456", "name": "Test Frame"}

            response = await design_agent.execute_task(request)

            assert response.status == AgentStatus.COMPLETED
            assert response.result["frame_id"] == "123:456"
            mock_execute.assert_called_once_with(request)


class TestComponentAgent:
    """Test suite for ComponentAgent."""
    
    @pytest_asyncio.fixture
    async def component_agent(self):
        """Create component agent instance."""
        with patch('agents.component_agent.GeminiClient') as mock_gemini, \
             patch('agents.component_agent.MCPClient') as mock_mcp:
            
            mock_gemini_instance = AsyncMock()
            mock_gemini.return_value = mock_gemini_instance
            
            mock_mcp_instance = AsyncMock()
            mock_mcp.return_value = mock_mcp_instance
            
            agent = ComponentAgent()
            await agent.initialize()
            return agent
    
    @pytest.mark.asyncio
    async def test_component_agent_capabilities(self, component_agent):
        """Test component agent capabilities."""
        assert AgentCapability.COMPONENT in component_agent.capabilities
        assert component_agent.name == "component_agent"
        
        actions = component_agent.get_supported_actions()
        expected_actions = [
            "create_component", "create_variant", "update_component",
            "create_component_set", "detach_instance", "create_instance",
            "override_properties", "swap_instance", "reset_overrides",
            "publish_component", "create_nested_component"
        ]

        for action in expected_actions:
            assert action in actions


class TestTextAgent:
    """Test suite for TextAgent."""
    
    @pytest_asyncio.fixture
    async def text_agent(self):
        """Create text agent instance."""
        with patch('agents.text_agent.GeminiClient') as mock_gemini, \
             patch('agents.text_agent.MCPClient') as mock_mcp:
            
            mock_gemini_instance = AsyncMock()
            mock_gemini.return_value = mock_gemini_instance
            
            mock_mcp_instance = AsyncMock()
            mock_mcp.return_value = mock_mcp_instance
            
            agent = TextAgent()
            await agent.initialize()
            return agent
    
    @pytest.mark.asyncio
    async def test_text_agent_capabilities(self, text_agent):
        """Test text agent capabilities."""
        assert AgentCapability.TEXT in text_agent.capabilities
        assert text_agent.name == "text_agent"
        
        actions = text_agent.get_supported_actions()
        expected_actions = [
            "create_text", "update_text", "format_text", "load_font",
            "apply_text_style", "create_text_style", "resize_text",
            "align_text", "set_line_height", "set_letter_spacing",
            "create_text_frame", "convert_to_outlines"
        ]

        for action in expected_actions:
            assert action in actions
    
    @pytest.mark.asyncio
    async def test_create_text_action(self, text_agent):
        """Test create text action."""
        request = AgentRequest(
            task_id="text_test_001",
            action="create_text",
            parameters={"content": "Hello World", "font_size": 16}
        )
        
        with patch.object(text_agent, '_execute_action') as mock_execute:
            mock_execute.return_value = {"text_id": "789:012", "content": "Hello World"}

            response = await text_agent.execute_task(request)

            assert response.status == AgentStatus.COMPLETED
            assert response.result["text_id"] == "789:012"
            mock_execute.assert_called_once_with(request)


class TestColorAgent:
    """Test suite for ColorAgent."""
    
    @pytest_asyncio.fixture
    async def color_agent(self):
        """Create color agent instance."""
        with patch('agents.color_agent.GeminiClient') as mock_gemini, \
             patch('agents.color_agent.MCPClient') as mock_mcp:
            
            mock_gemini_instance = AsyncMock()
            mock_gemini.return_value = mock_gemini_instance
            
            mock_mcp_instance = AsyncMock()
            mock_mcp.return_value = mock_mcp_instance
            
            agent = ColorAgent()
            await agent.initialize()
            return agent
    
    @pytest.mark.asyncio
    async def test_color_agent_capabilities(self, color_agent):
        """Test color agent capabilities."""
        assert AgentCapability.COLOR in color_agent.capabilities
        assert color_agent.name == "color_agent"
        
        actions = color_agent.get_supported_actions()
        expected_actions = [
            "apply_color", "create_color_style", "generate_palette",
            "extract_colors", "apply_gradient", "create_gradient_style",
            "adjust_opacity", "blend_colors", "create_color_variables",
            "apply_color_theme", "analyze_color_contrast"
        ]

        for action in expected_actions:
            assert action in actions


class TestImageAgent:
    """Test suite for ImageAgent."""
    
    @pytest_asyncio.fixture
    async def image_agent(self):
        """Create image agent instance."""
        with patch('agents.image_agent.GeminiClient') as mock_gemini, \
             patch('agents.image_agent.MCPClient') as mock_mcp:
            
            mock_gemini_instance = AsyncMock()
            mock_gemini.return_value = mock_gemini_instance
            
            mock_mcp_instance = AsyncMock()
            mock_mcp.return_value = mock_mcp_instance
            
            agent = ImageAgent()
            await agent.initialize()
            return agent
    
    @pytest.mark.asyncio
    async def test_image_agent_capabilities(self, image_agent):
        """Test image agent capabilities."""
        assert AgentCapability.IMAGE in image_agent.capabilities
        assert image_agent.name == "image_agent"
        
        actions = image_agent.get_supported_actions()
        expected_actions = [
            "upload_image", "create_icon", "replace_image", "crop_image",
            "resize_image", "apply_image_effects", "create_mask",
            "generate_placeholder", "optimize_image", "create_image_fill",
            "extract_image_colors", "create_pattern_fill"
        ]

        for action in expected_actions:
            assert action in actions


class TestPrototypeAgent:
    """Test suite for PrototypeAgent."""
    
    @pytest_asyncio.fixture
    async def prototype_agent(self):
        """Create prototype agent instance."""
        with patch('agents.prototype_agent.GeminiClient') as mock_gemini, \
             patch('agents.prototype_agent.MCPClient') as mock_mcp:
            
            mock_gemini_instance = AsyncMock()
            mock_gemini.return_value = mock_gemini_instance
            
            mock_mcp_instance = AsyncMock()
            mock_mcp.return_value = mock_mcp_instance
            
            agent = PrototypeAgent()
            await agent.initialize()
            return agent
    
    @pytest.mark.asyncio
    async def test_prototype_agent_capabilities(self, prototype_agent):
        """Test prototype agent capabilities."""
        assert AgentCapability.PROTOTYPE in prototype_agent.capabilities
        assert prototype_agent.name == "prototype_agent"
        
        actions = prototype_agent.get_supported_actions()
        expected_actions = [
            "create_interaction", "create_overlay", "set_transition",
            "create_flow", "add_hotspot", "create_smart_animate",
            "set_scroll_behavior", "create_component_state",
            "add_device_frame", "create_presentation", "set_prototype_settings"
        ]

        for action in expected_actions:
            assert action in actions


class TestExportAgent:
    """Test suite for ExportAgent."""
    
    @pytest_asyncio.fixture
    async def export_agent(self):
        """Create export agent instance."""
        with patch('agents.export_agent.GeminiClient') as mock_gemini, \
             patch('agents.export_agent.MCPClient') as mock_mcp:
            
            mock_gemini_instance = AsyncMock()
            mock_gemini.return_value = mock_gemini_instance
            
            mock_mcp_instance = AsyncMock()
            mock_mcp.return_value = mock_mcp_instance
            
            agent = ExportAgent()
            await agent.initialize()
            return agent
    
    @pytest.mark.asyncio
    async def test_export_agent_capabilities(self, export_agent):
        """Test export agent capabilities."""
        assert AgentCapability.EXPORT in export_agent.capabilities
        assert export_agent.name == "export_agent"
        
        actions = export_agent.get_supported_actions()
        expected_actions = [
            "export_png", "export_svg", "export_pdf", "export_jpg",
            "export_assets", "generate_code", "export_specs",
            "create_style_guide", "export_tokens", "batch_export",
            "export_prototype", "generate_handoff"
        ]

        for action in expected_actions:
            assert action in actions


class TestCollaborationAgent:
    """Test suite for CollaborationAgent."""
    
    @pytest_asyncio.fixture
    async def collaboration_agent(self):
        """Create collaboration agent instance."""
        with patch('agents.collaboration_agent.GeminiClient') as mock_gemini, \
             patch('agents.collaboration_agent.MCPClient') as mock_mcp:
            
            mock_gemini_instance = AsyncMock()
            mock_gemini.return_value = mock_gemini_instance
            
            mock_mcp_instance = AsyncMock()
            mock_mcp.return_value = mock_mcp_instance
            
            agent = CollaborationAgent()
            await agent.initialize()
            return agent
    
    @pytest.mark.asyncio
    async def test_collaboration_agent_capabilities(self, collaboration_agent):
        """Test collaboration agent capabilities."""
        assert AgentCapability.COLLABORATION in collaboration_agent.capabilities
        assert collaboration_agent.name == "collaboration_agent"
        
        actions = collaboration_agent.get_supported_actions()
        expected_actions = [
            "add_comment", "resolve_comment", "share_file", "set_permissions",
            "create_team", "invite_user", "create_branch", "merge_branch",
            "publish_library", "create_version", "track_changes", "sync_changes"
        ]

        for action in expected_actions:
            assert action in actions


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
