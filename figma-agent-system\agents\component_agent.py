"""
Component Agent for Figma Agent System.
Handles component creation, management, and variant systems.
"""

import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentRequest, AgentResponse, AgentStatus, AgentCapability
from config.agent_activities import FIGMA_ACTIVITIES
from tools.mcp_integration import MC<PERSON><PERSON>, ToolRegistry, MCPTool
from tools.figma_api import FigmaAPIClient
from llm.gemini_client import GeminiClient
from utils.logging_config import create_agent_logger


class ComponentAgent(BaseAgent):
    """
    Component Agent specializing in component systems and reusable elements.
    
    Capabilities:
    - Component creation and management
    - Component variants and properties
    - Component library organization
    - Instance management
    - Component documentation
    """
    
    def __init__(
        self,
        mcp_client: MCPClient = None,
        figma_client: FigmaAPIClient = None,
        gemini_client: GeminiClient = None
    ):
        super().__init__(
            name="component_agent",
            capabilities=[AgentCapability.COMPONENT]
        )
        
        # Initialize clients
        self.mcp_client = mcp_client or MCPClient()
        self.figma_client = figma_client or FigmaAPIClient()
        self.gemini_client = gemini_client or GeminiClient()
        
        # Initialize tool registry
        self.tool_registry = ToolRegistry(self.mcp_client)
        self._register_component_tools()
        
        # Agent-specific logger
        self.logger = create_agent_logger("component")
        
        # Component-specific metrics
        self.components_created = 0
        self.variants_created = 0

    def get_supported_actions(self) -> list:
        """Get list of actions this agent can handle."""
        return [
            "create_component", "create_variant", "update_component",
            "create_component_set", "detach_instance", "create_instance",
            "override_properties", "swap_instance", "reset_overrides",
            "publish_component", "create_nested_component"
        ]

    def _can_handle_action(self, action: str) -> bool:
        """Check if this agent can handle the given action."""
        return action in self.get_supported_actions()
    
    def _register_component_tools(self) -> None:
        """Register component-related tools."""
        
        # Create component tool
        create_component_tool = MCPTool(
            name="create_component",
            description="Create a new component from selected elements",
            parameters={
                "name": {"type": str, "required": True, "description": "Component name"},
                "description": {"type": str, "required": False, "description": "Component description"},
                "source_node_id": {"type": str, "required": True, "description": "Source node to convert"},
                "properties": {"type": dict, "required": False, "description": "Component properties"}
            },
            handler=self._handle_create_component,
            timeout=15.0,
            max_retries=2
        )
        self.tool_registry.register_tool(create_component_tool, "component_management")
        
        # Create variant tool
        create_variant_tool = MCPTool(
            name="create_variant",
            description="Create a component variant",
            parameters={
                "component_id": {"type": str, "required": True, "description": "Base component ID"},
                "variant_name": {"type": str, "required": True, "description": "Variant name"},
                "properties": {"type": dict, "required": True, "description": "Variant properties"}
            },
            handler=self._handle_create_variant,
            timeout=12.0,
            max_retries=2
        )
        self.tool_registry.register_tool(create_variant_tool, "component_management")
        
        # Create instance tool
        create_instance_tool = MCPTool(
            name="create_instance",
            description="Create an instance of a component",
            parameters={
                "component_id": {"type": str, "required": True, "description": "Component ID"},
                "x": {"type": float, "required": True, "description": "X position"},
                "y": {"type": float, "required": True, "description": "Y position"},
                "overrides": {"type": dict, "required": False, "description": "Property overrides"}
            },
            handler=self._handle_create_instance,
            timeout=10.0,
            max_retries=2
        )
        self.tool_registry.register_tool(create_instance_tool, "component_management")
    
    async def execute_task(self, request: AgentRequest) -> AgentResponse:
        """Execute a component task."""
        self.logger.info(
            "Executing component task",
            task_id=request.task_id,
            action=request.action
        )
        
        try:
            if not self._validate_request(request):
                return AgentResponse(
                    task_id=request.task_id,
                    agent_name=self.name,
                    status=AgentStatus.FAILED,
                    error="Invalid request parameters",
                    execution_time=0.0
                )
            
            if request.action not in FIGMA_ACTIVITIES:
                return AgentResponse(
                    task_id=request.task_id,
                    agent_name=self.name,
                    status=AgentStatus.FAILED,
                    error=f"Unsupported action: {request.action}",
                    execution_time=0.0
                )
            
            activity = FIGMA_ACTIVITIES[request.action]
            
            if activity.agent_capability not in self.capabilities:
                return AgentResponse(
                    task_id=request.task_id,
                    agent_name=self.name,
                    status=AgentStatus.FAILED,
                    error=f"Action '{request.action}' not supported by Component Agent",
                    execution_time=0.0
                )
            
            result = await self._execute_action(request.action, request.parameters, request.context)
            self._update_task_metrics(request.action)
            
            return AgentResponse(
                task_id=request.task_id,
                agent_name=self.name,
                status=AgentStatus.COMPLETED,
                result=result,
                execution_time=result.get("execution_time", 0.0),
                metadata={
                    "action": request.action,
                    "tools_used": result.get("tools_used", []),
                    "ai_assistance": result.get("ai_assistance", False)
                }
            )
            
        except Exception as e:
            self.logger.error(
                "Component task execution failed",
                task_id=request.task_id,
                action=request.action,
                error=str(e)
            )
            
            return AgentResponse(
                task_id=request.task_id,
                agent_name=self.name,
                status=AgentStatus.FAILED,
                error=str(e),
                execution_time=0.0
            )
    
    async def _execute_action(
        self,
        action: str,
        parameters: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute a specific component action."""
        
        action_tool_map = {
            "create_component": "create_component",
            "create_variant": "create_variant",
            "create_instance": "create_instance"
        }
        
        if action in action_tool_map:
            tool_name = action_tool_map[action]
            response = await self.tool_registry.execute_tool(tool_name, parameters, context)
            
            if response.status == "success":
                return {
                    "success": True,
                    "result": response.result,
                    "execution_time": response.execution_time,
                    "tools_used": [tool_name]
                }
            else:
                raise Exception(f"Tool execution failed: {response.error}")
        
        elif action == "organize_components":
            return await self._organize_components(parameters, context)
        
        else:
            raise Exception(f"Unknown action: {action}")
    
    async def _handle_create_component(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle component creation."""
        name = parameters.get("name")
        source_node_id = parameters.get("source_node_id")
        
        if not name or not source_node_id:
            raise ValueError("name and source_node_id are required")
        
        component_data = {
            "type": "COMPONENT",
            "name": name,
            "description": parameters.get("description", ""),
            "source_node_id": source_node_id,
            "properties": parameters.get("properties", {}),
            "created_at": datetime.utcnow().isoformat()
        }
        
        component_id = f"comp_{int(datetime.utcnow().timestamp())}"
        self.components_created += 1
        
        return {
            "component_id": component_id,
            "component_data": component_data,
            "success": True
        }
    
    async def _handle_create_variant(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle variant creation."""
        component_id = parameters.get("component_id")
        variant_name = parameters.get("variant_name")
        properties = parameters.get("properties", {})
        
        if not component_id or not variant_name:
            raise ValueError("component_id and variant_name are required")
        
        variant_data = {
            "type": "COMPONENT_VARIANT",
            "name": variant_name,
            "parent_component_id": component_id,
            "properties": properties,
            "created_at": datetime.utcnow().isoformat()
        }
        
        variant_id = f"var_{int(datetime.utcnow().timestamp())}"
        self.variants_created += 1
        
        return {
            "variant_id": variant_id,
            "variant_data": variant_data,
            "success": True
        }
    
    async def _handle_create_instance(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle instance creation."""
        component_id = parameters.get("component_id")
        x = parameters.get("x")
        y = parameters.get("y")
        
        if not component_id or x is None or y is None:
            raise ValueError("component_id, x, and y are required")
        
        instance_data = {
            "type": "INSTANCE",
            "component_id": component_id,
            "x": x,
            "y": y,
            "overrides": parameters.get("overrides", {}),
            "created_at": datetime.utcnow().isoformat()
        }
        
        instance_id = f"inst_{int(datetime.utcnow().timestamp())}"
        self.instances_created += 1
        
        return {
            "instance_id": instance_id,
            "instance_data": instance_data,
            "success": True
        }
    
    async def _organize_components(self, parameters: Dict, context: Dict) -> Dict[str, Any]:
        """Organize components using AI assistance."""
        components = parameters.get("components", [])
        organization_style = parameters.get("style", "category")
        
        if not components:
            raise ValueError("No components provided for organization")
        
        # Use AI to suggest organization
        organization_prompt = f"Organize {len(components)} components by {organization_style}"
        suggestions = await self.gemini_client.generate_design_suggestions(
            design_brief=organization_prompt,
            constraints={"component_count": len(components)}
        )
        
        organization_results = {
            "style": organization_style,
            "components_organized": len(components),
            "ai_suggestions": suggestions[0].__dict__ if suggestions else {},
            "success": True,
            "ai_assistance": True
        }
        
        return organization_results
    
    def _validate_request(self, request: AgentRequest) -> bool:
        """Validate component request."""
        if not request.task_id or not request.action:
            return False
        
        if request.action == "create_component":
            params = request.parameters
            if not params.get("name") or not params.get("source_node_id"):
                return False
        
        return True
    
    def _update_task_metrics(self, action: str) -> None:
        """Update task-specific metrics."""
        if action == "create_component":
            self.components_created += 1
        elif action == "create_variant":
            self.variants_created += 1
        elif action == "create_instance":
            self.instances_created += 1
    
    def get_agent_metrics(self) -> Dict[str, Any]:
        """Get agent-specific metrics."""
        base_metrics = super().get_agent_metrics()
        
        component_metrics = {
            "components_created": self.components_created,
            "variants_created": self.variants_created,
            "instances_created": self.instances_created
        }
        
        return {**base_metrics, **component_metrics}
    
    async def close(self) -> None:
        """Close agent and cleanup resources."""
        await self.tool_registry.close()
        await self.figma_client.close()
        self.logger.info("Component Agent closed")
