"""
Component Agent for Figma Agent System.
Handles component creation and management.
"""

import asyncio
import json
import websockets
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentRequest, AgentResponse, AgentStatus, AgentCapability


class ComponentAgent(BaseAgent):
    """
    Component Agent specializing in UI component creation.
    
    Capabilities:
    - Component creation
    - Button creation
    - Form elements
    """
    
    def __init__(self, channel_id: str = "oa34ym6m"):
        super().__init__(
            name="component",
            capabilities=[AgentCapability.COMPONENT]
        )
        
        self.channel_id = channel_id
        self.websocket_url = "ws://localhost:3055"
        self.logger = logging.getLogger(f"{__name__}.{self.name}")
        
        # State tracking
        self.components_created = 0
        
    async def initialize(self):
        """Initialize the component agent."""
        self.logger.info(f"🧩 Initializing Component Agent (channel: {self.channel_id})")
        
    async def process_request(self, request: Dict) -> Dict:
        """Process a component request."""
        task_id = request.get('task_id', 'unknown')
        action = request.get('action', 'create_components')
        parameters = request.get('parameters', {})
        
        self.logger.info(f"🎯 Processing {action} request: {task_id}")
        
        try:
            if action == 'create_components':
                result = await self._create_components(parameters)
            else:
                result = await self._create_components(parameters)
                
            return {
                'task_id': task_id,
                'agent_name': self.name,
                'status': 'completed',
                'success': True,
                'result': result,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ Failed to process {action}: {e}")
            return {
                'task_id': task_id,
                'agent_name': self.name,
                'status': 'failed',
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _create_components(self, parameters: Dict) -> Dict:
        """Create UI components."""
        prompt = parameters.get('prompt', '')
        
        # Determine component type from prompt
        component_type = 'button'  # Default
        if 'form' in prompt.lower():
            component_type = 'form'
        elif 'button' in prompt.lower():
            component_type = 'button'
        elif 'card' in prompt.lower():
            component_type = 'card'
            
        self.logger.info(f"🧩 Creating {component_type} component")
        
        # Send component creation command to Figma plugin
        component_result = await self._send_figma_command({
            "type": "create_component",
            "data": {
                "componentType": component_type,
                "prompt": prompt
            }
        })
        
        self.components_created += 1
        
        return {
            'component_id': f"comp_{int(datetime.now().timestamp())}",
            'type': component_type,
            'figma_response': component_result,
            'success': True
        }
    
    async def _send_figma_command(self, command: Dict) -> Dict:
        """Send a command to the Figma plugin via WebSocket using MCP protocol."""
        try:
            async with websockets.connect(self.websocket_url) as websocket:
                join_message = {"type": "join", "channel": self.channel_id}
                await websocket.send(json.dumps(join_message))

                join_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                self.logger.info(f"📡 Channel joined: {join_response}")

                # Convert to proper MCP command format
                mcp_command = self._convert_to_mcp_command(command)

                await websocket.send(json.dumps(mcp_command))
                self.logger.info(f"📤 Sent MCP command: {mcp_command}")

                response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                response_data = json.loads(response)

                self.logger.info(f"📥 Received response: {response_data}")
                return response_data

        except asyncio.TimeoutError:
            self.logger.warning("⏰ Figma command timeout")
            return {"status": "timeout", "message": "Command sent but no response received"}
        except Exception as e:
            self.logger.error(f"❌ WebSocket communication failed: {e}")
            raise e

    def _convert_to_mcp_command(self, command: Dict) -> Dict:
        """Convert generic command to MCP protocol format."""
        command_type = command.get("type", "unknown")
        command_data = command.get("data", {})

        if command_type == "create_component":
            return {
                "jsonrpc": "2.0",
                "id": f"comp_{int(asyncio.get_event_loop().time())}",
                "method": "tools/call",
                "params": {
                    "name": "create_figma_component",
                    "arguments": {
                        "componentType": command_data.get("componentType", "button"),
                        "prompt": command_data.get("prompt", "Create component")
                    }
                }
            }
        else:
            return command
    
    async def shutdown(self):
        """Shutdown the component agent."""
        self.logger.info(f"🛑 Shutting down Component Agent")
        self.logger.info(f"📊 Stats: {self.components_created} components created")
    
    # Abstract method implementations
    async def _execute_action(self, request: AgentRequest) -> Dict[str, Any]:
        """Execute the specific action requested."""
        return await self._create_components(request.parameters)
    
    def _can_handle_action(self, action: str) -> bool:
        """Check if this agent can handle the specified action."""
        return action in self.get_supported_actions()
    
    def get_supported_actions(self) -> List[str]:
        """Get list of actions this agent supports."""
        return ["create_components", "create_button", "create_form"]
