"""
FastAPI server for Figma Agent System.
Provides REST API endpoints for agent interactions and task management.
"""

import asyncio
import time
from typing import Dict, List, Optional
from datetime import datetime
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import uvicorn

try:
    from .models import (
        AgentRequestModel,
        AgentResponseModel,
        RouterRequestModel,
        RouterResponseModel,
        HealthResponse,
        MetricsResponse,
        ErrorResponse,
        TaskStatusModel
    )
except ImportError:
    from api.models import (
        AgentRequestModel,
        AgentResponseModel,
        RouterRequestModel,
        RouterResponseModel,
        HealthResponse,
        MetricsResponse,
        ErrorResponse,
        TaskStatusModel
    )
from agents.design_agent import DesignAgent
from agents.component_agent import ComponentAgent
from agents.text_agent import TextAgent
from agents.color_agent import ColorAgent
from agents.image_agent import ImageAgent
from agents.prototype_agent import PrototypeAgent
from agents.export_agent import ExportAgent
from agents.collaboration_agent import CollaborationAgent
from router.task_router import TaskRouter
from config.settings import settings
from utils.logging_config import setup_logging, StructuredLogger
from .agent_endpoints import register_all_agent_endpoints


class FigmaAgentAPI:
    """Main API class for Figma Agent System."""
    
    def __init__(self):
        self.start_time = time.time()
        self.logger = StructuredLogger("api")
        
        # Initialize all agents
        self.design_agent = DesignAgent()
        self.component_agent = ComponentAgent()
        self.text_agent = TextAgent()
        self.color_agent = ColorAgent()
        self.image_agent = ImageAgent()
        self.prototype_agent = PrototypeAgent()
        self.export_agent = ExportAgent()
        self.collaboration_agent = CollaborationAgent()

        # Initialize router
        self.task_router = TaskRouter()
        
        # API metrics
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.response_times = []
        self.active_tasks = 0
        
        # Task tracking
        self.task_status_cache: Dict[str, TaskStatusModel] = {}
        
        self.logger.info("Figma Agent API initialized")
    
    async def startup(self):
        """Startup tasks."""
        self.logger.info("Starting Figma Agent API")

        # Initialize all agents
        await self.design_agent.initialize()
        await self.component_agent.initialize()
        await self.text_agent.initialize()
        await self.color_agent.initialize()
        await self.image_agent.initialize()
        await self.prototype_agent.initialize()
        await self.export_agent.initialize()
        await self.collaboration_agent.initialize()

        # Register all agents with router
        self.task_router.register_agent(self.design_agent)
        self.task_router.register_agent(self.component_agent)
        self.task_router.register_agent(self.text_agent)
        self.task_router.register_agent(self.color_agent)
        self.task_router.register_agent(self.image_agent)
        self.task_router.register_agent(self.prototype_agent)
        self.task_router.register_agent(self.export_agent)
        self.task_router.register_agent(self.collaboration_agent)

        self.logger.info("Figma Agent API started successfully")
    
    async def shutdown(self):
        """Shutdown tasks."""
        self.logger.info("Shutting down Figma Agent API")

        # Close all agents
        await self.design_agent.close()
        await self.component_agent.close()
        await self.text_agent.close()
        await self.color_agent.close()
        await self.image_agent.close()
        await self.prototype_agent.close()
        await self.export_agent.close()
        await self.collaboration_agent.close()

        self.logger.info("Figma Agent API shutdown complete")
    
    def get_uptime(self) -> float:
        """Get service uptime in seconds."""
        return time.time() - self.start_time
    
    def update_metrics(self, success: bool, response_time: float):
        """Update API metrics."""
        self.total_requests += 1
        
        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
        
        # Keep rolling average of response times
        self.response_times.append(response_time)
        if len(self.response_times) > 1000:  # Keep last 1000 response times
            self.response_times.pop(0)
    
    def get_average_response_time(self) -> float:
        """Get average response time."""
        if not self.response_times:
            return 0.0
        return sum(self.response_times) / len(self.response_times)


# Global API instance
api_instance = FigmaAgentAPI()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    setup_logging()
    await api_instance.startup()
    
    yield
    
    # Shutdown
    await api_instance.shutdown()


# Create FastAPI app
app = FastAPI(
    title="Figma Agent System API",
    description="Multi-agent system for automating Figma design workflows",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.api.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)


# Dependency for request timing
async def track_request_time():
    """Dependency to track request timing."""
    start_time = time.time()
    yield start_time


# Register all agent endpoints
register_all_agent_endpoints(app, api_instance, track_request_time)


# Health check endpoint
@app.get("/health", response_model=HealthResponse, tags=["System"])
async def health_check():
    """Health check endpoint."""
    try:
        # Check component health
        components = {
            "design_agent": "healthy" if api_instance.design_agent else "unhealthy",
            "task_router": "healthy" if api_instance.task_router else "unhealthy",
            "gemini_client": "healthy",  # Could add actual health check
            "figma_api": "healthy",      # Could add actual health check
        }
        
        overall_status = "healthy" if all(
            status == "healthy" for status in components.values()
        ) else "unhealthy"
        
        return HealthResponse(
            status=overall_status,
            uptime=api_instance.get_uptime(),
            components=components
        )
        
    except Exception as e:
        api_instance.logger.error("Health check failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service unhealthy"
        )


# Metrics endpoint
@app.get("/metrics", response_model=MetricsResponse, tags=["System"])
async def get_metrics():
    """Get system metrics."""
    try:
        # Get agent metrics
        agent_metrics = {
            "design_agent": api_instance.design_agent.get_agent_metrics()
        }
        
        # System metrics (could be expanded with actual system monitoring)
        system_metrics = {
            "uptime": api_instance.get_uptime(),
            "active_tasks": api_instance.active_tasks,
            "memory_usage": "N/A",  # Could add actual memory monitoring
            "cpu_usage": "N/A"      # Could add actual CPU monitoring
        }
        
        return MetricsResponse(
            total_requests=api_instance.total_requests,
            successful_requests=api_instance.successful_requests,
            failed_requests=api_instance.failed_requests,
            average_response_time=api_instance.get_average_response_time(),
            active_tasks=api_instance.active_tasks,
            agent_metrics=agent_metrics,
            system_metrics=system_metrics
        )
        
    except Exception as e:
        api_instance.logger.error("Failed to get metrics", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve metrics"
        )


# Router endpoint for natural language queries
@app.post("/router/process", response_model=RouterResponseModel, tags=["Router"])
async def process_router_request(
    request: RouterRequestModel,
    background_tasks: BackgroundTasks,
    start_time: float = Depends(track_request_time)
):
    """Process a natural language request through the router."""
    try:
        api_instance.logger.info(
            "Processing router request",
            request_id=request.request_id,
            query_length=len(request.user_query)
        )
        
        # Convert to router request format with proper type conversion
        from router.task_router import RouterRequest, TaskPriority

        # Convert priority int to TaskPriority enum
        priority_map = {1: TaskPriority.LOW, 5: TaskPriority.NORMAL, 8: TaskPriority.HIGH, 10: TaskPriority.CRITICAL}
        task_priority = priority_map.get(request.priority, TaskPriority.NORMAL)

        router_request = RouterRequest(
            request_id=request.request_id,
            user_query=request.user_query,
            context=request.context if request.context else None,
            execution_strategy=request.execution_strategy,
            priority=task_priority,
            timeout=int(request.timeout) if request.timeout else None
        )
        
        # Process through router
        response = await api_instance.task_router.process_request(router_request)
        
        # Update metrics
        execution_time = time.time() - start_time
        api_instance.update_metrics(True, execution_time)
        
        # Convert response to API model
        # Convert agent_responses to task_results
        task_results = []
        for agent_resp in response.agent_responses:
            task_results.append(AgentResponseModel(
                task_id=agent_resp.task_id,
                agent_id=agent_resp.agent_name,  # Use agent_name as agent_id
                status=agent_resp.status.value if hasattr(agent_resp.status, 'value') else str(agent_resp.status),
                result=agent_resp.result,
                error=agent_resp.error,
                execution_time=agent_resp.execution_time,
                metadata=agent_resp.metadata or {}
            ))

        return RouterResponseModel(
            request_id=response.request_id,
            status=response.status,
            tasks_created=[],  # Task creation info not available in current RouterResponse
            task_results=task_results,
            execution_plan=response.metadata.get('task_plan', {}) if response.metadata else {},
            total_execution_time=response.execution_time,
            metadata=response.metadata or {},
            timestamp=response.timestamp
        )
        
    except Exception as e:
        execution_time = time.time() - start_time
        api_instance.update_metrics(False, execution_time)
        
        api_instance.logger.error(
            "Router request failed",
            request_id=request.request_id,
            error=str(e)
        )

        # Debug logging
        print(f"DEBUG: Router error: {str(e)}")
        import traceback
        traceback.print_exc()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Request processing failed: {str(e)}"
        )


# Direct agent endpoint
@app.post("/agents/design/execute", response_model=AgentResponseModel, tags=["Agents"])
async def execute_design_task(
    request: AgentRequestModel,
    start_time: float = Depends(track_request_time)
):
    """Execute a task directly on the Design Agent."""
    try:
        api_instance.logger.info(
            "Executing design task",
            task_id=request.task_id,
            action=request.action
        )
        
        api_instance.active_tasks += 1
        
        # Convert to agent request format
        from agents.base_agent import AgentRequest

        agent_request = AgentRequest(
            task_id=request.task_id,
            action=request.action,
            parameters=request.parameters,
            context=request.context,
            priority=request.priority,
            timeout=request.timeout
        )

        # Execute task
        response = await api_instance.design_agent.execute_task(agent_request)
        
        api_instance.active_tasks -= 1
        
        # Update metrics
        execution_time = time.time() - start_time
        success = response.status == "completed"
        api_instance.update_metrics(success, execution_time)
        
        # Convert response to API model
        return AgentResponseModel(
            task_id=response.task_id,
            agent_id=response.agent_id,
            status=response.status.value,
            result=response.result,
            error=response.error,
            execution_time=response.execution_time,
            metadata=response.metadata
        )
        
    except Exception as e:
        api_instance.active_tasks = max(0, api_instance.active_tasks - 1)
        execution_time = time.time() - start_time
        api_instance.update_metrics(False, execution_time)
        
        api_instance.logger.error(
            "Design task execution failed",
            task_id=request.task_id,
            error=str(e)
        )

        # Debug logging
        print(f"DEBUG: Design agent error: {str(e)}")
        import traceback
        traceback.print_exc()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Task execution failed: {str(e)}"
        )


# Component Agent endpoint
@app.post("/agents/component/execute", response_model=AgentResponseModel, tags=["Agents"])
async def execute_component_task(
    request: AgentRequestModel,
    start_time: float = Depends(track_request_time)
):
    """Execute a task directly on the Component Agent."""
    try:
        api_instance.logger.info(
            "Executing component task",
            task_id=request.task_id,
            action=request.action
        )

        api_instance.active_tasks += 1

        # Convert to agent request format
        from agents.base_agent import AgentRequest

        agent_request = AgentRequest(
            task_id=request.task_id,
            action=request.action,
            parameters=request.parameters,
            context=request.context,
            priority=request.priority,
            timeout=request.timeout
        )

        # Execute task
        response = await api_instance.component_agent.execute_task(agent_request)

        api_instance.active_tasks -= 1

        # Update metrics
        execution_time = time.time() - start_time
        success = response.status == "completed"
        api_instance.update_metrics(success, execution_time)

        # Convert response to API model
        return AgentResponseModel(
            task_id=response.task_id,
            agent_id=response.agent_id,
            status=response.status.value,
            result=response.result,
            error=response.error,
            execution_time=response.execution_time,
            metadata=response.metadata
        )

    except Exception as e:
        api_instance.active_tasks = max(0, api_instance.active_tasks - 1)
        execution_time = time.time() - start_time
        api_instance.update_metrics(False, execution_time)

        api_instance.logger.error(
            "Component task execution failed",
            task_id=request.task_id,
            error=str(e)
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Task execution failed: {str(e)}"
        )


# Text Agent endpoint
@app.post("/agents/text/execute", response_model=AgentResponseModel, tags=["Agents"])
async def execute_text_task(
    request: AgentRequestModel,
    start_time: float = Depends(track_request_time)
):
    """Execute a task directly on the Text Agent."""
    try:
        api_instance.logger.info(
            "Executing text task",
            task_id=request.task_id,
            action=request.action
        )

        api_instance.active_tasks += 1

        # Convert to agent request format
        from agents.base_agent import AgentRequest

        agent_request = AgentRequest(
            task_id=request.task_id,
            action=request.action,
            parameters=request.parameters,
            context=request.context,
            priority=request.priority,
            timeout=request.timeout
        )

        # Execute task
        response = await api_instance.text_agent.execute_task(agent_request)

        api_instance.active_tasks -= 1

        # Update metrics
        execution_time = time.time() - start_time
        success = response.status == "completed"
        api_instance.update_metrics(success, execution_time)

        # Convert response to API model
        return AgentResponseModel(
            task_id=response.task_id,
            agent_id=response.agent_id,
            status=response.status.value,
            result=response.result,
            error=response.error,
            execution_time=response.execution_time,
            metadata=response.metadata
        )

    except Exception as e:
        api_instance.active_tasks = max(0, api_instance.active_tasks - 1)
        execution_time = time.time() - start_time
        api_instance.update_metrics(False, execution_time)

        api_instance.logger.error(
            "Text task execution failed",
            task_id=request.task_id,
            error=str(e)
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Task execution failed: {str(e)}"
        )


# Task status endpoint
@app.get("/tasks/{task_id}/status", response_model=TaskStatusModel, tags=["Tasks"])
async def get_task_status(task_id: str):
    """Get the status of a specific task."""
    try:
        # Check cache first
        if task_id in api_instance.task_status_cache:
            return api_instance.task_status_cache[task_id]
        
        # If not in cache, return not found
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        api_instance.logger.error(
            "Failed to get task status",
            task_id=task_id,
            error=str(e)
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve task status"
        )


# Error handler
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            error=exc.detail,
            error_code=f"HTTP_{exc.status_code}",
            details={"status_code": exc.status_code}
        ).model_dump()
    )


# Generic exception handler
@app.exception_handler(Exception)
async def generic_exception_handler(request, exc):
    """Handle generic exceptions."""
    api_instance.logger.error("Unhandled exception", error=str(exc))
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            error="Internal server error",
            error_code="INTERNAL_ERROR",
            details={"exception_type": type(exc).__name__}
        ).model_dump()
    )


# Development server runner
if __name__ == "__main__":
    uvicorn.run(
        "api.fastapi_server:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.debug,
        log_level="info"
    )
