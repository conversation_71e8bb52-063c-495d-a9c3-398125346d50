#!/usr/bin/env python3
"""
Channel Configuration Test and Management Script.

This script helps you test and configure channel communication between
the Figma Agent System and your Claude Talk to Figma MCP server.
"""

import sys
import os
import asyncio
import json
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Set test environment variables
os.environ["GEMINI_API_KEY"] = "test-key"
os.environ["FIGMA_ACCESS_TOKEN"] = "test-token"

async def test_channel_connection(channel_id: str = "oa34ym6m"):
    """Test connection to a specific channel."""
    print(f"🔌 Testing Channel Connection: {channel_id}")
    
    try:
        from tools.figma_mcp_tools import get_claude_talk_to_figma_client
        
        # Create client with specific channel
        client = get_claude_talk_to_figma_client(channel_id=channel_id)
        
        # Test channel connection
        result = await client.test_channel_connection(channel_id)
        
        if result["success"]:
            print(f"✅ Channel connection successful!")
            print(f"   Channel: {result['channel']}")
            print(f"   System message: {result['system_message']['message']}")
            print(f"   Join response: {result['join_response']['message']}")
        else:
            print(f"❌ Channel connection failed!")
            print(f"   Channel: {result['channel']}")
            print(f"   Error: {result['error']}")
        
        await client.close()
        return result["success"]
        
    except Exception as e:
        print(f"❌ Channel test failed: {e}")
        return False

async def test_agent_channel_configuration():
    """Test agent-specific channel configuration."""
    print("\n🤖 Testing Agent Channel Configuration...")
    
    try:
        from tools.figma_mcp_tools import get_claude_talk_to_figma_client
        from config.channel_config import get_channel_config
        
        # Test single channel configuration (recommended)
        print("\n📋 Testing Single Channel Configuration:")
        client = get_claude_talk_to_figma_client(channel_id="oa34ym6m")
        
        # Test channel for different agent contexts
        test_contexts = [
            {"agent_name": "design_agent"},
            {"agent_name": "color_agent"},
            {"agent_name": "text_agent"},
            {"channel_id": "oa34ym6m"}  # Explicit channel override
        ]
        
        for context in test_contexts:
            channel = client._get_channel_for_context(context)
            agent_name = context.get("agent_name", "explicit")
            print(f"   {agent_name}: {channel}")
        
        await client.close()
        
        # Test multi-channel configuration
        print("\n📋 Testing Multi-Channel Configuration:")
        multi_client = get_claude_talk_to_figma_client(
            channel_id="1re91tfd",
            channel_per_agent={
                "design_agent": "oa34ym6m",
                "color_agent": "oa34ym6m",
                "test_agent": "test-channel"
            }
        )
        
        for context in test_contexts:
            channel = multi_client._get_channel_for_context(context)
            agent_name = context.get("agent_name", "explicit")
            print(f"   {agent_name}: {channel}")
        
        await multi_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Agent channel configuration test failed: {e}")
        return False

async def test_channel_communication_flow():
    """Test the complete channel communication flow."""
    print("\n🔄 Testing Complete Channel Communication Flow...")
    
    try:
        from tools.figma_mcp_tools import get_claude_talk_to_figma_client
        
        # Create client with your active channel
        client = get_claude_talk_to_figma_client(channel_id="oa34ym6m")
        
        # Test tool execution with channel context
        print("🚀 Testing tool execution with channel context...")
        
        # Create context with your active channel
        context = {
            "agent_name": "design_agent",
            "channel_id": "oa34ym6m",
            "task_id": "test-task-123"
        }
        
        # Test a simple tool call
        try:
            response = await client.execute_tool(
                "get_figma_data",
                {"fileKey": "test-file", "nodeId": "test-node"},
                context
            )
            
            print(f"✅ Tool execution successful:")
            print(f"   Status: {response.status}")
            print(f"   Channel used: {context['channel_id']}")
            print(f"   Execution time: {response.execution_time}s")
            
        except Exception as tool_error:
            print(f"⚠️  Tool execution failed (expected for test): {tool_error}")
        
        await client.close()
        return True
        
    except Exception as e:
        print(f"❌ Channel communication flow test failed: {e}")
        return False

def print_channel_configuration_guide():
    """Print comprehensive channel configuration guide."""
    print("\n" + "=" * 80)
    print("📚 CHANNEL CONFIGURATION GUIDE")
    print("=" * 80)
    
    print("""
🎯 YOUR ACTIVE FIGMA PLUGIN CHANNEL: 1re91tfd

Based on your screenshot, your Claude Talk to Figma plugin is using channel "1re91tfd".
This is the channel ID you should use for all agent communications.

🔧 CONFIGURATION OPTIONS:

1. **Single Channel (Recommended)**:
   All agents use your active Figma plugin channel.
   
   ```python
   from tools.figma_mcp_tools import get_claude_talk_to_figma_client
   
   client = get_claude_talk_to_figma_client(channel_id="1re91tfd")
   ```

2. **Agent-Specific Channels**:
   Different agents can use different channels if needed.
   
   ```python
   client = get_claude_talk_to_figma_client(
       channel_id="1re91tfd",  # Default channel
       channel_per_agent={
           "design_agent": "1re91tfd",
           "color_agent": "1re91tfd"
       }
   )
   ```

3. **Context-Based Channel Selection**:
   Specify channel in execution context.
   
   ```python
   context = {
       "agent_name": "design_agent",
       "channel_id": "1re91tfd"  # Your active channel
   }
   
   response = await client.execute_tool("get_figma_data", params, context)
   ```

🚀 AGENT EXECUTION WITH CHANNELS:

When you send requests to your agents via the FastAPI API, you can specify
the channel in the request:

```bash
curl -X POST "http://localhost:8000/agents/design/execute" \\
  -H "Content-Type: application/json" \\
  -d '{
    "task_description": "Get Figma file information",
    "parameters": {
      "fileKey": "your-figma-file-key"
    },
    "context": {
      "channel_id": "oa34ym6m"
    }
  }'
```

🔍 CHANNEL COMMUNICATION FLOW:

1. Agent receives task with context
2. MCP client extracts channel ID from context
3. WebSocket connects to ws://localhost:3055
4. Client joins specified channel ("1re91tfd")
5. Tool request sent to your MCP server
6. MCP server communicates with Figma plugin on same channel
7. Results returned through the chain

🛠️ TROUBLESHOOTING CHANNELS:

1. **Channel Not Found**:
   - Ensure your Figma plugin is active
   - Verify channel "1re91tfd" is connected
   - Check MCP server logs

2. **Connection Timeout**:
   - Increase channel_timeout in configuration
   - Check WebSocket connectivity
   - Verify Figma is open and plugin is loaded

3. **Tool Execution Fails**:
   - Ensure you're using the correct channel ID
   - Verify Figma file permissions
   - Check that the plugin has access to the file

💡 BEST PRACTICES:

✅ Use your active channel "1re91tfd" for all operations
✅ Set reasonable timeouts (10-15 seconds)
✅ Include channel context in agent requests
✅ Test channel connectivity before tool execution
✅ Monitor MCP server logs for channel activity

❌ Don't use random or non-existent channel IDs
❌ Don't set very short timeouts (< 5 seconds)
❌ Don't forget to include channel context
❌ Don't assume channels persist between sessions

🎉 Your channel configuration is ready!
   Default channel: 1re91tfd (your active Figma plugin channel)
   All agents will use this channel automatically.
""")

async def main():
    """Run all channel configuration tests."""
    print("🚀 Channel Configuration Tests for Claude Talk to Figma")
    print("=" * 70)
    
    success = True
    
    # Test your active channel connection
    print("Testing your active Figma plugin channel...")
    if not await test_channel_connection("oa34ym6m"):
        success = False
    
    # Test agent channel configuration
    if not await test_agent_channel_configuration():
        success = False
    
    # Test complete communication flow
    if not await test_channel_communication_flow():
        success = False
    
    # Print configuration guide
    print_channel_configuration_guide()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Channel configuration is working correctly!")
        print(f"\n✅ Your agents are configured to use channel: 1re91tfd")
        print("✅ Channel joining and communication protocols are working")
        print("✅ Agent-specific channel routing is functional")
    else:
        print("❌ Some channel configuration tests failed.")
        print("\n🔧 Check the errors above and ensure:")
        print("1. Your Claude Talk to Figma MCP server is running")
        print("2. Your Figma plugin is active on channel 1re91tfd")
        print("3. WebSocket connectivity is working")
    
    print("\n🚀 Next steps:")
    print("1. Use channel '1re91tfd' in all agent requests")
    print("2. Include channel context in API calls")
    print("3. Monitor channel connectivity in production")

if __name__ == "__main__":
    asyncio.run(main())
