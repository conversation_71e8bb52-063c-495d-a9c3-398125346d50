"""
Color Agent for Figma Agent System.
Handles color application, theming, and styling.
"""

import asyncio
import json
import websockets
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentRequest, AgentResponse, AgentStatus, AgentCapability


class ColorAgent(BaseAgent):
    """
    Color Agent specializing in color application and styling.
    
    Capabilities:
    - Color application
    - Theme management
    - Style coordination
    """
    
    def __init__(self, channel_id: str = "oa34ym6m"):
        super().__init__(
            name="color",
            capabilities=[AgentCapability.COLOR]
        )
        
        self.channel_id = channel_id
        self.websocket_url = "ws://localhost:3055"
        self.logger = logging.getLogger(f"{__name__}.{self.name}")
        
        # State tracking
        self.colors_applied = 0
        
    async def initialize(self):
        """Initialize the color agent."""
        self.logger.info(f"🎨 Initializing Color Agent (channel: {self.channel_id})")
        
    async def process_request(self, request: Dict) -> Dict:
        """Process a color request."""
        task_id = request.get('task_id', 'unknown')
        action = request.get('action', 'apply_styling')
        parameters = request.get('parameters', {})
        
        self.logger.info(f"🎯 Processing {action} request: {task_id}")
        
        try:
            if action == 'apply_styling':
                result = await self._apply_styling(parameters)
            else:
                result = await self._apply_styling(parameters)
                
            return {
                'task_id': task_id,
                'agent_name': self.name,
                'status': 'completed',
                'success': True,
                'result': result,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ Failed to process {action}: {e}")
            return {
                'task_id': task_id,
                'agent_name': self.name,
                'status': 'failed',
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _apply_styling(self, parameters: Dict) -> Dict:
        """Apply styling and colors."""
        primary_color = parameters.get('primary_color', 'blue')
        
        self.logger.info(f"🎨 Applying styling with primary color: {primary_color}")
        
        # Send styling command to Figma plugin
        style_result = await self._send_figma_command({
            "type": "apply_style",
            "data": {
                "primaryColor": primary_color,
                "action": "style_elements"
            }
        })
        
        self.colors_applied += 1
        
        return {
            'style_id': f"style_{int(datetime.now().timestamp())}",
            'primary_color': primary_color,
            'figma_response': style_result,
            'success': True
        }
    
    async def _send_figma_command(self, command: Dict) -> Dict:
        """Send a command to the Figma plugin via WebSocket using MCP protocol."""
        try:
            async with websockets.connect(self.websocket_url) as websocket:
                join_message = {"type": "join", "channel": self.channel_id}
                await websocket.send(json.dumps(join_message))

                join_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                self.logger.info(f"📡 Channel joined: {join_response}")

                # Convert to proper MCP command format
                mcp_command = self._convert_to_mcp_command(command)

                await websocket.send(json.dumps(mcp_command))
                self.logger.info(f"📤 Sent MCP command: {mcp_command}")

                response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                response_data = json.loads(response)

                self.logger.info(f"📥 Received response: {response_data}")
                return response_data

        except asyncio.TimeoutError:
            self.logger.warning("⏰ Figma command timeout")
            return {"status": "timeout", "message": "Command sent but no response received"}
        except Exception as e:
            self.logger.error(f"❌ WebSocket communication failed: {e}")
            raise e

    def _convert_to_mcp_command(self, command: Dict) -> Dict:
        """Convert generic command to MCP protocol format."""
        command_type = command.get("type", "unknown")
        command_data = command.get("data", {})

        if command_type == "apply_style":
            return {
                "jsonrpc": "2.0",
                "id": f"style_{int(asyncio.get_event_loop().time())}",
                "method": "tools/call",
                "params": {
                    "name": "apply_figma_styling",
                    "arguments": {
                        "primaryColor": command_data.get("primaryColor", "blue"),
                        "action": command_data.get("action", "style_elements")
                    }
                }
            }
        else:
            return command
    
    async def shutdown(self):
        """Shutdown the color agent."""
        self.logger.info(f"🛑 Shutting down Color Agent")
        self.logger.info(f"📊 Stats: {self.colors_applied} colors applied")
    
    # Abstract method implementations
    async def _execute_action(self, request: AgentRequest) -> Dict[str, Any]:
        """Execute the specific action requested."""
        return await self._apply_styling(request.parameters)
    
    def _can_handle_action(self, action: str) -> bool:
        """Check if this agent can handle the specified action."""
        return action in self.get_supported_actions()
    
    def get_supported_actions(self) -> List[str]:
        """Get list of actions this agent supports."""
        return ["apply_styling", "set_theme", "apply_colors"]
