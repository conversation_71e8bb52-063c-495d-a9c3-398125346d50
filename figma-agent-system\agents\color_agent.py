"""
Color Agent for Figma Agent System.
Handles color management, palettes, and styling operations.
"""

import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentRequest, AgentResponse, AgentStatus, AgentCapability
from config.agent_activities import FIGMA_ACTIVITIES
from tools.mcp_integration import MC<PERSON><PERSON>, ToolRegistry, MCPTool
from tools.figma_api import FigmaAPIClient
from llm.gemini_client import GeminiClient
from utils.logging_config import create_agent_logger
from utils.validation import validate_color_value


class ColorAgent(BaseAgent):
    """
    Color Agent specializing in color systems and styling.
    
    Capabilities:
    - Color palette creation and management
    - Color application and styling
    - Color system organization
    - Accessibility compliance
    - Color harmony analysis
    """
    
    def __init__(
        self,
        mcp_client: MCPClient = None,
        figma_client: FigmaAPIClient = None,
        gemini_client: GeminiClient = None
    ):
        super().__init__(
            name="color_agent",
            capabilities=[AgentCapability.COLOR]
        )
        
        # Initialize clients
        self.mcp_client = mcp_client or MCPClient()
        self.figma_client = figma_client or FigmaAPIClient()
        self.gemini_client = gemini_client or GeminiClient()
        
        # Initialize tool registry
        self.tool_registry = ToolRegistry(self.mcp_client)
        self._register_color_tools()
        
        # Agent-specific logger
        self.logger = create_agent_logger("color")
        
        # Color-specific metrics
        self.palettes_created = 0
        self.colors_applied = 0
        self.styles_created = 0
        
        self.logger.info("Color Agent initialized")

    def get_supported_actions(self) -> list:
        """Get list of actions this agent can handle."""
        return [
            "apply_color", "create_color_style", "generate_palette",
            "extract_colors", "apply_gradient", "create_gradient_style",
            "adjust_opacity", "blend_colors", "create_color_variables",
            "apply_color_theme", "analyze_color_contrast"
        ]

    def _can_handle_action(self, action: str) -> bool:
        """Check if this agent can handle the given action."""
        return action in self.get_supported_actions()

    def _register_color_tools(self) -> None:
        """Register color-related tools."""
        
        # Create color palette tool
        create_palette_tool = MCPTool(
            name="create_color_palette",
            description="Create a color palette",
            parameters={
                "name": {"type": str, "required": True, "description": "Palette name"},
                "base_color": {"type": dict, "required": False, "description": "Base color for generation"},
                "palette_type": {"type": str, "required": False, "default": "complementary", "description": "Palette type"},
                "color_count": {"type": int, "required": False, "default": 5, "description": "Number of colors"}
            },
            handler=self._handle_create_palette,
            timeout=12.0,
            max_retries=2
        )
        self.tool_registry.register_tool(create_palette_tool, "color_management")
        
        # Apply color tool
        apply_color_tool = MCPTool(
            name="apply_color",
            description="Apply color to an element",
            parameters={
                "node_id": {"type": str, "required": True, "description": "Target node ID"},
                "color": {"type": dict, "required": True, "description": "Color to apply"},
                "property": {"type": str, "required": False, "default": "fill", "description": "Color property (fill, stroke)"}
            },
            handler=self._handle_apply_color,
            timeout=8.0,
            max_retries=2
        )
        self.tool_registry.register_tool(apply_color_tool, "color_management")
        
        # Create color style tool
        create_style_tool = MCPTool(
            name="create_color_style",
            description="Create a reusable color style",
            parameters={
                "name": {"type": str, "required": True, "description": "Style name"},
                "color": {"type": dict, "required": True, "description": "Color definition"},
                "description": {"type": str, "required": False, "description": "Style description"}
            },
            handler=self._handle_create_style,
            timeout=10.0,
            max_retries=2
        )
        self.tool_registry.register_tool(create_style_tool, "color_management")
    
    async def execute_task(self, request: AgentRequest) -> AgentResponse:
        """Execute a color task."""
        self.logger.info(
            "Executing color task",
            task_id=request.task_id,
            action=request.action
        )
        
        try:
            if not self._validate_request(request):
                return AgentResponse(
                    task_id=request.task_id,
                    agent_id=self.agent_id,
                    status=AgentStatus.FAILED,
                    error="Invalid request parameters"
                )
            
            if request.action not in FIGMA_ACTIVITIES:
                return AgentResponse(
                    task_id=request.task_id,
                    agent_id=self.agent_id,
                    status=AgentStatus.FAILED,
                    error=f"Unsupported action: {request.action}"
                )
            
            activity = FIGMA_ACTIVITIES[request.action]
            
            if activity.agent_capability not in self.capabilities:
                return AgentResponse(
                    task_id=request.task_id,
                    agent_id=self.agent_id,
                    status=AgentStatus.FAILED,
                    error=f"Action '{request.action}' not supported by Color Agent"
                )
            
            result = await self._execute_action(request.action, request.parameters, request.context)
            self._update_task_metrics(request.action)
            
            return AgentResponse(
                task_id=request.task_id,
                agent_id=self.agent_id,
                status=AgentStatus.COMPLETED,
                result=result,
                execution_time=result.get("execution_time", 0.0),
                metadata={
                    "action": request.action,
                    "tools_used": result.get("tools_used", []),
                    "ai_assistance": result.get("ai_assistance", False)
                }
            )
            
        except Exception as e:
            self.logger.error(
                "Color task execution failed",
                task_id=request.task_id,
                action=request.action,
                error=str(e)
            )
            
            return AgentResponse(
                task_id=request.task_id,
                agent_id=self.agent_id,
                status=AgentStatus.FAILED,
                error=str(e)
            )
    
    async def _execute_action(
        self,
        action: str,
        parameters: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute a specific color action."""
        
        action_tool_map = {
            "create_color_palette": "create_color_palette",
            "apply_color": "apply_color",
            "create_color_style": "create_color_style"
        }
        
        if action in action_tool_map:
            tool_name = action_tool_map[action]
            response = await self.tool_registry.execute_tool(tool_name, parameters, context)
            
            if response.status == "success":
                return {
                    "success": True,
                    "result": response.result,
                    "execution_time": response.execution_time,
                    "tools_used": [tool_name]
                }
            else:
                raise Exception(f"Tool execution failed: {response.error}")
        
        elif action == "generate_color_scheme":
            return await self._generate_color_scheme(parameters, context)
        
        else:
            raise Exception(f"Unknown action: {action}")
    
    async def _handle_create_palette(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle color palette creation."""
        name = parameters.get("name")
        if not name:
            raise ValueError("name is required")
        
        base_color = parameters.get("base_color")
        palette_type = parameters.get("palette_type", "complementary")
        color_count = parameters.get("color_count", 5)
        
        # Generate palette using AI if base color provided
        if base_color and validate_color_value(base_color):
            palette = await self.gemini_client.generate_color_palette(
                base_color=base_color,
                palette_type=palette_type,
                color_count=color_count
            )
            colors = palette.colors if palette else []
        else:
            # Generate default palette
            colors = self._generate_default_palette(palette_type, color_count)
        
        palette_data = {
            "name": name,
            "type": palette_type,
            "colors": colors,
            "color_count": len(colors),
            "created_at": datetime.utcnow().isoformat()
        }
        
        palette_id = f"palette_{int(datetime.utcnow().timestamp())}"
        self.palettes_created += 1
        
        return {
            "palette_id": palette_id,
            "palette_data": palette_data,
            "success": True,
            "ai_assistance": bool(base_color)
        }
    
    async def _handle_apply_color(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle color application."""
        node_id = parameters.get("node_id")
        color = parameters.get("color")
        
        if not node_id or not color:
            raise ValueError("node_id and color are required")
        
        if not validate_color_value(color):
            raise ValueError("Invalid color format")
        
        property_type = parameters.get("property", "fill")
        
        color_data = {
            "node_id": node_id,
            "color": color,
            "property": property_type,
            "applied_at": datetime.utcnow().isoformat()
        }
        
        self.colors_applied += 1
        
        return {
            "color_data": color_data,
            "success": True
        }
    
    async def _handle_create_style(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle color style creation."""
        name = parameters.get("name")
        color = parameters.get("color")
        
        if not name or not color:
            raise ValueError("name and color are required")
        
        if not validate_color_value(color):
            raise ValueError("Invalid color format")
        
        style_data = {
            "name": name,
            "color": color,
            "description": parameters.get("description", ""),
            "type": "COLOR_STYLE",
            "created_at": datetime.utcnow().isoformat()
        }
        
        style_id = f"style_{int(datetime.utcnow().timestamp())}"
        self.styles_created += 1
        
        return {
            "style_id": style_id,
            "style_data": style_data,
            "success": True
        }
    
    async def _generate_color_scheme(self, parameters: Dict, context: Dict) -> Dict[str, Any]:
        """Generate color scheme using AI assistance."""
        theme = parameters.get("theme", "modern")
        purpose = parameters.get("purpose", "web_design")
        
        # Use AI to generate comprehensive color scheme
        color_scheme = await self.gemini_client.generate_color_palette(
            base_color=None,
            palette_type="comprehensive",
            color_count=12,
            theme=theme,
            purpose=purpose
        )
        
        if not color_scheme:
            raise Exception("Failed to generate color scheme")
        
        scheme_data = {
            "theme": theme,
            "purpose": purpose,
            "primary_colors": color_scheme.colors[:3],
            "secondary_colors": color_scheme.colors[3:6],
            "accent_colors": color_scheme.colors[6:9],
            "neutral_colors": color_scheme.colors[9:12],
            "accessibility_compliant": True,  # Would check actual compliance
            "success": True,
            "ai_assistance": True
        }
        
        return scheme_data
    
    def _generate_default_palette(self, palette_type: str, color_count: int) -> List[Dict[str, Any]]:
        """Generate a default color palette."""
        default_palettes = {
            "complementary": [
                {"r": 0.2, "g": 0.4, "b": 0.8, "a": 1.0},
                {"r": 0.8, "g": 0.6, "b": 0.2, "a": 1.0},
                {"r": 0.1, "g": 0.2, "b": 0.4, "a": 1.0},
                {"r": 0.4, "g": 0.3, "b": 0.1, "a": 1.0},
                {"r": 0.6, "g": 0.7, "b": 0.9, "a": 1.0}
            ],
            "monochromatic": [
                {"r": 0.2, "g": 0.2, "b": 0.2, "a": 1.0},
                {"r": 0.4, "g": 0.4, "b": 0.4, "a": 1.0},
                {"r": 0.6, "g": 0.6, "b": 0.6, "a": 1.0},
                {"r": 0.8, "g": 0.8, "b": 0.8, "a": 1.0},
                {"r": 0.9, "g": 0.9, "b": 0.9, "a": 1.0}
            ]
        }
        
        palette = default_palettes.get(palette_type, default_palettes["complementary"])
        return palette[:color_count]
    
    def _validate_request(self, request: AgentRequest) -> bool:
        """Validate color request."""
        if not request.task_id or not request.action:
            return False
        
        if request.action == "apply_color":
            params = request.parameters
            if not params.get("node_id") or not params.get("color"):
                return False
        
        return True
    
    def _update_task_metrics(self, action: str) -> None:
        """Update task-specific metrics."""
        if action == "create_color_palette":
            self.palettes_created += 1
        elif action == "apply_color":
            self.colors_applied += 1
        elif action == "create_color_style":
            self.styles_created += 1
    
    def get_agent_metrics(self) -> Dict[str, Any]:
        """Get agent-specific metrics."""
        base_metrics = super().get_agent_metrics()
        
        color_metrics = {
            "palettes_created": self.palettes_created,
            "colors_applied": self.colors_applied,
            "styles_created": self.styles_created
        }
        
        return {**base_metrics, **color_metrics}
    
    async def close(self) -> None:
        """Close agent and cleanup resources."""
        await self.tool_registry.close()
        await self.figma_client.close()
        self.logger.info("Color Agent closed")
