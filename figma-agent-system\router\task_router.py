"""
Task Router for Figma Agent System.
Handles intelligent task orchestration and agent coordination.
"""

import asyncio
import logging
import re
from typing import Dict, List, Optional, Set, Tuple, Union
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field

from agents.base_agent import BaseAgent, AgentRequest, AgentResponse, AgentCapability, AgentStatus


class ExecutionStrategy(str, Enum):
    """Task execution strategies."""
    SEQUENTIAL = "sequential"  # Execute tasks one after another
    CONCURRENT = "concurrent"  # Execute tasks simultaneously
    PIPELINE = "pipeline"      # Execute with dependencies
    BROADCAST = "broadcast"    # Send same task to multiple agents


class TaskPriority(int, Enum):
    """Task priority levels."""
    LOW = 1
    NORMAL = 5
    HIGH = 8
    CRITICAL = 10


class RouterRequest(BaseModel):
    """Request format for the task router."""
    
    request_id: str = Field(..., description="Unique request identifier")
    user_query: str = Field(..., description="Natural language user request")
    context: Optional[Dict] = Field(default=None, description="Additional context")
    priority: TaskPriority = Field(default=TaskPriority.NORMAL, description="Request priority")
    timeout: Optional[int] = Field(default=None, description="Request timeout in seconds")
    execution_strategy: ExecutionStrategy = Field(default=ExecutionStrategy.SEQUENTIAL, description="Execution strategy")


class RouterResponse(BaseModel):
    """Response format from the task router."""
    
    request_id: str = Field(..., description="Request identifier")
    status: str = Field(..., description="Overall execution status")
    agent_responses: List[AgentResponse] = Field(default_factory=list, description="Individual agent responses")
    execution_time: float = Field(..., description="Total execution time")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")
    metadata: Dict = Field(default_factory=dict, description="Additional metadata")


class TaskRouter:
    """
    Intelligent task router that orchestrates agent execution.
    Analyzes user requests and coordinates appropriate agents.
    """
    
    def __init__(self, llm_client=None):
        self.agents: Dict[str, BaseAgent] = {}
        self.capability_map: Dict[AgentCapability, List[str]] = {}
        self.llm_client = llm_client
        self.logger = logging.getLogger("router")
        
        # Request tracking
        self.active_requests: Dict[str, asyncio.Task] = {}
        self.request_history: List[RouterResponse] = []
        
        # Performance metrics
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
    
    def register_agent(self, agent: BaseAgent) -> None:
        """Register an agent with the router."""
        self.agents[agent.name] = agent
        
        # Update capability mapping
        for capability in agent.capabilities:
            if capability not in self.capability_map:
                self.capability_map[capability] = []
            self.capability_map[capability].append(agent.name)
        
        self.logger.info(f"Registered agent: {agent.name} with capabilities: {agent.capabilities}")
    
    def unregister_agent(self, agent_name: str) -> bool:
        """Unregister an agent from the router."""
        if agent_name in self.agents:
            agent = self.agents.pop(agent_name)
            
            # Update capability mapping
            for capability in agent.capabilities:
                if capability in self.capability_map:
                    self.capability_map[capability] = [
                        name for name in self.capability_map[capability] 
                        if name != agent_name
                    ]
                    if not self.capability_map[capability]:
                        del self.capability_map[capability]
            
            self.logger.info(f"Unregistered agent: {agent_name}")
            return True
        return False
    
    def get_available_agents(self) -> List[str]:
        """Get list of available agents."""
        return [
            name for name, agent in self.agents.items()
            if agent.is_available
        ]
    
    def get_agent_status(self) -> Dict[str, Dict]:
        """Get status of all registered agents."""
        return {
            name: {
                "status": agent.status,
                "load": agent.current_load,
                "capabilities": agent.capabilities,
                "metrics": agent.performance_metrics
            }
            for name, agent in self.agents.items()
        }
    
    async def process_request(self, request: RouterRequest) -> RouterResponse:
        """
        Process a user request by analyzing it and routing to appropriate agents.
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Parse and analyze the request
            task_plan = await self._analyze_request(request)
            
            # Execute the task plan
            agent_responses = await self._execute_task_plan(task_plan, request)
            
            # Create response
            execution_time = asyncio.get_event_loop().time() - start_time
            response = RouterResponse(
                request_id=request.request_id,
                status="completed",
                agent_responses=agent_responses,
                execution_time=execution_time,
                metadata={
                    "task_plan": task_plan,
                    "agents_used": [resp.agent_name for resp in agent_responses]
                }
            )
            
            self.successful_requests += 1
            self.logger.info(f"Request {request.request_id} completed successfully")
            
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            response = RouterResponse(
                request_id=request.request_id,
                status="failed",
                agent_responses=[],
                execution_time=execution_time,
                metadata={"error": str(e)}
            )
            
            self.failed_requests += 1
            self.logger.error(f"Request {request.request_id} failed: {str(e)}")
        
        finally:
            self.total_requests += 1
            self.request_history.append(response)
            
            # Limit history size
            if len(self.request_history) > 100:
                self.request_history = self.request_history[-100:]
        
        return response
    
    async def _analyze_request(self, request: RouterRequest) -> Dict:
        """
        Analyze user request to determine required agents and actions.
        Uses LLM for intelligent parsing if available.
        """
        # Basic keyword-based analysis (can be enhanced with LLM)
        task_plan = {
            "tasks": [],
            "execution_strategy": request.execution_strategy,
            "dependencies": []
        }
        
        query_lower = request.user_query.lower()
        
        # Map keywords to capabilities and actions
        capability_keywords = {
            AgentCapability.DESIGN: ["frame", "layout", "grid", "resize", "position", "arrange"],
            AgentCapability.COMPONENT: ["component", "variant", "instance", "library"],
            AgentCapability.TEXT: ["text", "font", "typography", "label", "heading"],
            AgentCapability.COLOR: ["color", "fill", "gradient", "shadow", "stroke"],
            AgentCapability.IMAGE: ["image", "icon", "photo", "asset"],
            AgentCapability.PROTOTYPE: ["prototype", "link", "interaction", "animation"],
            AgentCapability.EXPORT: ["export", "download", "save", "png", "svg"],
            AgentCapability.COLLABORATION: ["comment", "share", "collaborate"]
        }
        
        # Determine required capabilities
        required_capabilities = set()
        for capability, keywords in capability_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                required_capabilities.add(capability)
        
        # If no specific capabilities detected, default to design
        if not required_capabilities:
            required_capabilities.add(AgentCapability.DESIGN)
        
        # Create tasks for each required capability
        for capability in required_capabilities:
            if capability in self.capability_map:
                available_agents = [
                    name for name in self.capability_map[capability]
                    if self.agents[name].is_available
                ]
                
                if available_agents:
                    # Select best agent (for now, just pick the first available)
                    selected_agent = available_agents[0]
                    
                    task_plan["tasks"].append({
                        "agent_name": selected_agent,
                        "capability": capability,
                        "action": self._infer_action(query_lower, capability),
                        "parameters": self._extract_parameters(request.user_query, capability)
                    })
        
        return task_plan
    
    def _infer_action(self, query: str, capability: AgentCapability) -> str:
        """Infer specific action based on query and capability."""
        # Enhanced action inference with specific action mapping
        query_lower = query.lower()

        action_keywords = {
            AgentCapability.DESIGN: {
                "create_frame": ["create frame", "add frame", "new frame", "frame", "create a frame", "create button", "create component"],
                "create_component": ["create component", "component", "new component"],
                "apply_auto_layout": ["auto layout", "layout", "arrange"],
                "create_grid_system": ["grid", "grid system"],
                "optimize_layout": ["optimize", "improve layout"]
            },
            AgentCapability.TEXT: {
                "create_text": ["add text", "create text", "insert text", "text"],
                "format_text": ["format text", "style text", "font"],
                "update_text": ["edit text", "change text", "update text"]
            },
            AgentCapability.COMPONENT: {
                "create_component": ["create component", "component", "new component"],
                "create_variant": ["variant", "create variant"],
                "create_instance": ["instance", "create instance"]
            },
            # Add more mappings as needed
        }
        
        if capability in action_keywords:
            for action, keywords in action_keywords[capability].items():
                if any(keyword in query_lower for keyword in keywords):
                    return action
        
        # Default actions
        default_actions = {
            AgentCapability.DESIGN: "create_frame",
            AgentCapability.COMPONENT: "create_component",
            AgentCapability.TEXT: "add_text",
            AgentCapability.COLOR: "apply_color",
            AgentCapability.IMAGE: "add_image",
            AgentCapability.PROTOTYPE: "create_link",
            AgentCapability.EXPORT: "export_frame",
            AgentCapability.COLLABORATION: "add_comment"
        }
        
        return default_actions.get(capability, "execute")
    
    def _extract_parameters(self, query: str, capability: AgentCapability) -> Dict:
        """Extract parameters from user query."""
        # Basic parameter extraction (can be enhanced with LLM)
        parameters = {}
        
        # Extract common parameters
        if "width" in query.lower():
            # Try to extract width value
            width_match = re.search(r'width[:\s]*(\d+)', query.lower())
            if width_match:
                parameters["width"] = int(width_match.group(1))
        
        if "height" in query.lower():
            height_match = re.search(r'height[:\s]*(\d+)', query.lower())
            if height_match:
                parameters["height"] = int(height_match.group(1))
        
        # Add capability-specific parameter extraction
        if capability == AgentCapability.TEXT:
            # Extract text content
            text_patterns = [
                r'"([^"]*)"',  # Text in quotes
                r"'([^']*)'",  # Text in single quotes
            ]
            for pattern in text_patterns:
                match = re.search(pattern, query)
                if match:
                    parameters["text"] = match.group(1)
                    break
        
        return parameters
    
    async def _execute_task_plan(
        self, 
        task_plan: Dict, 
        request: RouterRequest
    ) -> List[AgentResponse]:
        """Execute the task plan using the specified strategy."""
        tasks = task_plan["tasks"]
        strategy = task_plan["execution_strategy"]
        
        if strategy == ExecutionStrategy.SEQUENTIAL:
            return await self._execute_sequential(tasks, request)
        elif strategy == ExecutionStrategy.CONCURRENT:
            return await self._execute_concurrent(tasks, request)
        elif strategy == ExecutionStrategy.PIPELINE:
            return await self._execute_pipeline(tasks, request)
        elif strategy == ExecutionStrategy.BROADCAST:
            return await self._execute_broadcast(tasks, request)
        else:
            raise ValueError(f"Unknown execution strategy: {strategy}")
    
    async def _execute_sequential(self, tasks: List[Dict], request: RouterRequest) -> List[AgentResponse]:
        """Execute tasks sequentially."""
        responses = []
        
        for i, task in enumerate(tasks):
            agent_name = task["agent_name"]
            agent = self.agents[agent_name]
            
            agent_request = AgentRequest(
                task_id=f"{request.request_id}_task_{i}",
                action=task["action"],
                parameters=task["parameters"],
                context=request.context,
                priority=request.priority,
                timeout=request.timeout
            )
            
            response = await agent.execute_task(agent_request)
            responses.append(response)
            
            # Stop if task failed and it's critical
            if response.status == AgentStatus.FAILED and request.priority == TaskPriority.CRITICAL:
                break
        
        return responses
    
    async def _execute_concurrent(self, tasks: List[Dict], request: RouterRequest) -> List[AgentResponse]:
        """Execute tasks concurrently."""
        agent_tasks = []
        
        for i, task in enumerate(tasks):
            agent_name = task["agent_name"]
            agent = self.agents[agent_name]
            
            agent_request = AgentRequest(
                task_id=f"{request.request_id}_task_{i}",
                action=task["action"],
                parameters=task["parameters"],
                context=request.context,
                priority=request.priority,
                timeout=request.timeout
            )
            
            agent_tasks.append(agent.execute_task(agent_request))
        
        responses = await asyncio.gather(*agent_tasks, return_exceptions=True)
        
        # Handle exceptions
        processed_responses = []
        for response in responses:
            if isinstance(response, Exception):
                # Create error response with all required fields
                error_response = AgentResponse(
                    task_id="unknown",
                    agent_name="unknown",
                    status=AgentStatus.FAILED,
                    result=None,
                    error=str(response),
                    execution_time=0.0
                )
                processed_responses.append(error_response)
            else:
                processed_responses.append(response)
        
        return processed_responses
    
    async def _execute_pipeline(self, tasks: List[Dict], request: RouterRequest) -> List[AgentResponse]:
        """Execute tasks with dependencies (pipeline)."""
        # For now, implement as sequential (can be enhanced with dependency graph)
        return await self._execute_sequential(tasks, request)
    
    async def _execute_broadcast(self, tasks: List[Dict], request: RouterRequest) -> List[AgentResponse]:
        """Broadcast same task to multiple agents."""
        # For now, implement as concurrent (can be enhanced for true broadcasting)
        return await self._execute_concurrent(tasks, request)
