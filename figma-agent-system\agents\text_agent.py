"""
Text Agent for Figma Agent System.
Handles text creation, formatting, and typography management.
"""

import asyncio
import json
import websockets
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentRequest, AgentResponse, AgentStatus, AgentCapability
from tools.figma_mcp_tools import get_figma_mcp_client


class TextAgent(BaseAgent):
    """
    Text Agent specializing in text elements and typography.
    
    Capabilities:
    - Text creation and editing
    - Typography and font management
    - Text styling and formatting
    """
    
    def __init__(self, channel_id: str = "oa34ym6m"):
        super().__init__(
            name="text",
            capabilities=[AgentCapability.TEXT]
        )
        
        self.channel_id = channel_id
        self.websocket_url = "ws://localhost:3055"
        self.logger = logging.getLogger(f"{__name__}.{self.name}")
        
        # State tracking
        self.texts_created = 0
        
    async def initialize(self):
        """Initialize the text agent."""
        self.logger.info(f"📝 Initializing Text Agent (channel: {self.channel_id})")
        
    async def process_request(self, request: Dict) -> Dict:
        """Process a text request."""
        task_id = request.get('task_id', 'unknown')
        action = request.get('action', 'add_text')
        parameters = request.get('parameters', {})
        
        self.logger.info(f"🎯 Processing {action} request: {task_id}")
        
        try:
            if action == 'add_text':
                result = await self._add_text(parameters)
            else:
                result = await self._add_text(parameters)  # Default to add text
                
            return {
                'task_id': task_id,
                'agent_name': self.name,
                'status': 'completed',
                'success': True,
                'result': result,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"❌ Failed to process {action}: {e}")
            return {
                'task_id': task_id,
                'agent_name': self.name,
                'status': 'failed',
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _add_text(self, parameters: Dict) -> Dict:
        """Add text to Figma."""
        text_content = parameters.get('text', 'Sample Text')
        x = parameters.get('x', 50)
        y = parameters.get('y', 50)
        font_size = parameters.get('font_size', 16)
        
        self.logger.info(f"📝 Adding text: '{text_content}' at ({x}, {y})")
        
        # Send text creation command to Figma plugin
        text_result = await self._send_figma_command({
            "type": "create_text",
            "data": {
                "text": text_content,
                "x": x,
                "y": y,
                "fontSize": font_size
            }
        })
        
        self.texts_created += 1
        
        return {
            'text_id': f"text_{int(datetime.now().timestamp())}",
            'content': text_content,
            'position': {'x': x, 'y': y},
            'font_size': font_size,
            'figma_response': text_result,
            'success': True
        }
    
    async def _send_figma_command(self, command: Dict) -> Dict:
        """Send a command to the Figma plugin via WebSocket using MCP protocol."""
        try:
            async with websockets.connect(self.websocket_url) as websocket:
                # Join the channel
                join_message = {
                    "type": "join",
                    "channel": self.channel_id
                }
                await websocket.send(json.dumps(join_message))

                # Wait for join confirmation
                join_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                self.logger.info(f"📡 Channel joined: {join_response}")

                # Convert to proper MCP command format
                mcp_command = self._convert_to_mcp_command(command)

                # Send the MCP command
                await websocket.send(json.dumps(mcp_command))
                self.logger.info(f"📤 Sent MCP command: {mcp_command}")

                # Wait for response
                response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                response_data = json.loads(response)

                self.logger.info(f"📥 Received response: {response_data}")
                return response_data

        except asyncio.TimeoutError:
            self.logger.warning("⏰ Figma command timeout")
            return {"status": "timeout", "message": "Command sent but no response received"}
        except Exception as e:
            self.logger.error(f"❌ WebSocket communication failed: {e}")
            raise e

    def _convert_to_mcp_command(self, command: Dict) -> Dict:
        """Convert generic command to MCP protocol format."""
        command_type = command.get("type", "unknown")
        command_data = command.get("data", {})

        if command_type == "create_text":
            return {
                "jsonrpc": "2.0",
                "id": f"text_{int(asyncio.get_event_loop().time())}",
                "method": "tools/call",
                "params": {
                    "name": "create_figma_text",
                    "arguments": {
                        "text": command_data.get("text", "Sample Text"),
                        "x": command_data.get("x", 50),
                        "y": command_data.get("y", 50),
                        "fontSize": command_data.get("font_size", 16)
                    }
                }
            }
        else:
            return command
    
    async def shutdown(self):
        """Shutdown the text agent."""
        self.logger.info(f"🛑 Shutting down Text Agent")
        self.logger.info(f"📊 Stats: {self.texts_created} texts created")
    
    # Abstract method implementations
    async def _execute_action(self, request: AgentRequest) -> Dict[str, Any]:
        """Execute the specific action requested."""
        action = request.action
        parameters = request.parameters
        
        if action == 'add_text':
            return await self._add_text(parameters)
        else:
            return await self._add_text(parameters)
    
    def _can_handle_action(self, action: str) -> bool:
        """Check if this agent can handle the specified action."""
        supported_actions = self.get_supported_actions()
        return action in supported_actions
    
    def get_supported_actions(self) -> List[str]:
        """Get list of actions this agent supports."""
        return [
            "add_text",
            "format_text",
            "style_text"
        ]
