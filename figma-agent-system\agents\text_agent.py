"""
Text Agent for Figma Agent System.
Handles text creation, formatting, and typography management.
"""

import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentRequest, AgentResponse, AgentStatus, AgentCapability
from config.agent_activities import FIGMA_ACTIVITIES
from tools.mcp_integration import MC<PERSON><PERSON>, ToolRegistry, MCPTool
from tools.figma_api import FigmaAPIClient
from llm.gemini_client import GeminiClient
from utils.logging_config import create_agent_logger
from utils.validation import sanitize_string


class TextAgent(BaseAgent):
    """
    Text Agent specializing in text elements and typography.
    
    Capabilities:
    - Text creation and editing
    - Typography and font management
    - Text styling and formatting
    - Content generation
    - Text layout optimization
    """
    
    def __init__(
        self,
        mcp_client: MCPClient = None,
        figma_client: FigmaAPIClient = None,
        gemini_client: GeminiClient = None
    ):
        super().__init__(
            name="text_agent",
            capabilities=[AgentCapability.TEXT]
        )
        
        # Initialize clients
        self.mcp_client = mcp_client or MCPClient()
        self.figma_client = figma_client or FigmaAPIClient()
        self.gemini_client = gemini_client or GeminiClient()
        
        # Initialize tool registry
        self.tool_registry = ToolRegistry(self.mcp_client)
        self._register_text_tools()
        
        # Agent-specific logger
        self.logger = create_agent_logger("text")
        
        # Text-specific metrics
        self.text_elements_created = 0
        self.text_content_generated = 0
        self.fonts_applied = 0
        
        self.logger.info("Text Agent initialized")

    def get_supported_actions(self) -> list:
        """Get list of actions this agent can handle."""
        return [
            "create_text", "update_text", "format_text", "load_font",
            "apply_text_style", "create_text_style", "resize_text",
            "align_text", "set_line_height", "set_letter_spacing",
            "create_text_frame", "convert_to_outlines"
        ]

    def _can_handle_action(self, action: str) -> bool:
        """Check if this agent can handle the given action."""
        return action in self.get_supported_actions()

    def _register_text_tools(self) -> None:
        """Register text-related tools."""
        
        # Create text tool
        create_text_tool = MCPTool(
            name="create_text",
            description="Create a new text element",
            parameters={
                "content": {"type": str, "required": True, "description": "Text content"},
                "x": {"type": float, "required": True, "description": "X position"},
                "y": {"type": float, "required": True, "description": "Y position"},
                "font_family": {"type": str, "required": False, "default": "Inter", "description": "Font family"},
                "font_size": {"type": float, "required": False, "default": 16, "description": "Font size"},
                "font_weight": {"type": str, "required": False, "default": "Regular", "description": "Font weight"},
                "color": {"type": dict, "required": False, "description": "Text color"}
            },
            handler=self._handle_create_text,
            timeout=8.0,
            max_retries=2
        )
        self.tool_registry.register_tool(create_text_tool, "text_management")
        
        # Format text tool
        format_text_tool = MCPTool(
            name="format_text",
            description="Apply formatting to existing text",
            parameters={
                "node_id": {"type": str, "required": True, "description": "Text node ID"},
                "font_family": {"type": str, "required": False, "description": "Font family"},
                "font_size": {"type": float, "required": False, "description": "Font size"},
                "font_weight": {"type": str, "required": False, "description": "Font weight"},
                "color": {"type": dict, "required": False, "description": "Text color"},
                "alignment": {"type": str, "required": False, "description": "Text alignment"}
            },
            handler=self._handle_format_text,
            timeout=10.0,
            max_retries=2
        )
        self.tool_registry.register_tool(format_text_tool, "text_management")
        
        # Generate content tool
        generate_content_tool = MCPTool(
            name="generate_content",
            description="Generate text content using AI",
            parameters={
                "content_type": {"type": str, "required": True, "description": "Type of content to generate"},
                "length": {"type": str, "required": False, "default": "medium", "description": "Content length"},
                "tone": {"type": str, "required": False, "default": "professional", "description": "Content tone"},
                "context": {"type": dict, "required": False, "description": "Additional context"}
            },
            handler=self._handle_generate_content,
            timeout=15.0,
            max_retries=2
        )
        self.tool_registry.register_tool(generate_content_tool, "content_generation")
    

    
    async def _execute_action(self, request: AgentRequest) -> Dict[str, Any]:
        """Execute a specific text action."""
        action = request.action
        parameters = request.parameters
        context = request.context or {}
        
        action_tool_map = {
            "create_text": "create_text",
            "format_text": "format_text",
            "generate_content": "generate_content"
        }
        
        if action in action_tool_map:
            tool_name = action_tool_map[action]
            response = await self.tool_registry.execute_tool(tool_name, parameters, context)
            
            if response.status == "success":
                return {
                    "success": True,
                    "result": response.result,
                    "execution_time": response.execution_time,
                    "tools_used": [tool_name]
                }
            else:
                raise Exception(f"Tool execution failed: {response.error}")
        
        elif action == "optimize_typography":
            return await self._optimize_typography(parameters, context)
        
        else:
            raise Exception(f"Unknown action: {action}")
    
    async def _handle_create_text(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle text creation."""
        content = parameters.get("content")
        x = parameters.get("x")
        y = parameters.get("y")
        
        if not content or x is None or y is None:
            raise ValueError("content, x, and y are required")
        
        # Sanitize content
        content = sanitize_string(content)
        
        text_data = {
            "type": "TEXT",
            "characters": content,
            "x": x,
            "y": y,
            "fontName": {
                "family": parameters.get("font_family", "Inter"),
                "style": parameters.get("font_weight", "Regular")
            },
            "fontSize": parameters.get("font_size", 16),
            "fills": [parameters.get("color", {"type": "SOLID", "color": {"r": 0, "g": 0, "b": 0}})],
            "textAlignHorizontal": "LEFT",
            "textAlignVertical": "TOP"
        }
        
        text_id = f"text_{int(datetime.utcnow().timestamp())}"
        self.text_elements_created += 1
        
        return {
            "text_id": text_id,
            "text_data": text_data,
            "success": True
        }
    
    async def _handle_format_text(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle text formatting."""
        node_id = parameters.get("node_id")
        if not node_id:
            raise ValueError("node_id is required")
        
        format_data = {}
        
        if parameters.get("font_family") or parameters.get("font_weight"):
            format_data["fontName"] = {
                "family": parameters.get("font_family", "Inter"),
                "style": parameters.get("font_weight", "Regular")
            }
        
        if parameters.get("font_size"):
            format_data["fontSize"] = parameters.get("font_size")
        
        if parameters.get("color"):
            format_data["fills"] = [parameters.get("color")]
        
        if parameters.get("alignment"):
            format_data["textAlignHorizontal"] = parameters.get("alignment").upper()
        
        self.fonts_applied += 1
        
        return {
            "node_id": node_id,
            "format_data": format_data,
            "success": True
        }
    
    async def _handle_generate_content(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle content generation using AI."""
        content_type = parameters.get("content_type")
        if not content_type:
            raise ValueError("content_type is required")
        
        # Generate content using Gemini
        content_prompt = f"Generate {content_type} content"
        if parameters.get("length"):
            content_prompt += f" with {parameters.get('length')} length"
        if parameters.get("tone"):
            content_prompt += f" in {parameters.get('tone')} tone"
        
        generated_content = await self.gemini_client.generate_content(
            content_type="TEXT_CONTENT",
            prompt=content_prompt,
            max_length=self._get_length_limit(parameters.get("length", "medium"))
        )
        
        if not generated_content:
            raise Exception("Failed to generate content")
        
        self.text_content_generated += 1
        
        return {
            "content_type": content_type,
            "generated_content": generated_content,
            "length": parameters.get("length", "medium"),
            "tone": parameters.get("tone", "professional"),
            "success": True,
            "ai_assistance": True
        }
    
    async def _optimize_typography(self, parameters: Dict, context: Dict) -> Dict[str, Any]:
        """Optimize typography using AI assistance."""
        text_elements = parameters.get("text_elements", [])
        if not text_elements:
            raise ValueError("No text elements provided for optimization")
        
        # Use AI to suggest typography improvements
        typography_suggestions = await self.gemini_client.generate_design_suggestions(
            design_brief=f"Optimize typography for {len(text_elements)} text elements",
            style_preferences=["typography", "readability"],
            constraints={"element_count": len(text_elements)}
        )
        
        optimization_results = {
            "elements_optimized": len(text_elements),
            "typography_suggestions": typography_suggestions[0].__dict__ if typography_suggestions else {},
            "success": True,
            "ai_assistance": True
        }
        
        return optimization_results
    
    def _get_length_limit(self, length: str) -> int:
        """Get character limit based on length specification."""
        length_limits = {
            "short": 100,
            "medium": 300,
            "long": 800,
            "very_long": 1500
        }
        return length_limits.get(length.lower(), 300)
    
    def _validate_request(self, request: AgentRequest) -> bool:
        """Validate text request."""
        if not request.task_id or not request.action:
            return False
        
        if request.action == "create_text":
            params = request.parameters
            if not params.get("content") or params.get("x") is None or params.get("y") is None:
                return False
        
        return True
    
    def _update_task_metrics(self, action: str) -> None:
        """Update task-specific metrics."""
        if action == "create_text":
            self.text_elements_created += 1
        elif action == "generate_content":
            self.text_content_generated += 1
        elif action == "format_text":
            self.fonts_applied += 1
    
    def get_agent_metrics(self) -> Dict[str, Any]:
        """Get agent-specific metrics."""
        base_metrics = super().get_agent_metrics()
        
        text_metrics = {
            "text_elements_created": self.text_elements_created,
            "text_content_generated": self.text_content_generated,
            "fonts_applied": self.fonts_applied
        }
        
        return {**base_metrics, **text_metrics}
    
    async def close(self) -> None:
        """Close agent and cleanup resources."""
        await self.tool_registry.close()
        await self.figma_client.close()
        self.logger.info("Text Agent closed")
