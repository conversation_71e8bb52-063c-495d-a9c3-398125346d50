#!/usr/bin/env python3
"""
Figma Agent System - Main Entry Point

OpenAI SDK-style multi-agent architecture for Figma automation.
Provides intelligent routing and coordination of specialized agents.
"""

import asyncio
import logging
import os
from typing import Dict, List, Optional
from datetime import datetime

from router.agent_router import Agent<PERSON>outer
from agents.design_agent import DesignAgent
from agents.text_agent import TextAgent
from agents.color_agent import ColorAgent
from agents.component_agent import ComponentAgent
from config.settings import Settings
from utils.logging_config import setup_logging


class FigmaAgentSystem:
    """
    Main Figma Agent System class.
    Manages multiple specialized agents and provides a unified interface.
    """
    
    def __init__(self, channel_id: str = "oa34ym6m"):
        """Initialize the Figma Agent System."""
        self.channel_id = channel_id
        self.settings = Settings()
        self.logger = logging.getLogger(__name__)
        
        # Initialize router and agents
        self.router = None
        self.agents = {}
        
    async def initialize(self):
        """Initialize all agents and the router."""
        self.logger.info("🚀 Initializing Figma Agent System")
        
        # Create specialized agents
        self.agents = {
            'design': DesignAgent(channel_id=self.channel_id),
            'text': TextAgent(channel_id=self.channel_id),
            'color': ColorAgent(channel_id=self.channel_id),
            'component': ComponentAgent(channel_id=self.channel_id)
        }
        
        # Initialize all agents
        for name, agent in self.agents.items():
            await agent.initialize()
            self.logger.info(f"✅ {name.title()} Agent initialized")
        
        # Create and initialize router
        self.router = AgentRouter(agents=list(self.agents.values()))
        await self.router.initialize()
        
        self.logger.info("🎉 Figma Agent System ready!")
        
    async def process_request(self, prompt: str, context: Optional[Dict] = None) -> Dict:
        """
        Process a natural language request through the agent system.
        
        Args:
            prompt: Natural language description of what to create/modify
            context: Optional context information
            
        Returns:
            Dict containing the results from all agents that processed the request
        """
        if not self.router:
            raise RuntimeError("System not initialized. Call initialize() first.")
            
        self.logger.info(f"📝 Processing request: {prompt}")
        
        # Route the request to appropriate agents
        result = await self.router.route_request(
            prompt=prompt,
            context=context or {}
        )
        
        return result
    
    async def shutdown(self):
        """Shutdown all agents and cleanup resources."""
        self.logger.info("🛑 Shutting down Figma Agent System")
        
        # Shutdown all agents
        for name, agent in self.agents.items():
            await agent.shutdown()
            self.logger.info(f"✅ {name.title()} Agent shutdown")
            
        if self.router:
            await self.router.shutdown()
            
        self.logger.info("👋 Figma Agent System shutdown complete")


async def main():
    """Main function for testing the system."""
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)

    # Create and initialize system
    system = FigmaAgentSystem()

    try:
        logger.info("🚀 Initializing Figma Agent System")
        await system.initialize()

        # Test with the user's requested prompt
        logger.info("🧪 Testing system with user's test prompt...")
        print("\n🎨 Figma Agent System - Test Mode")
        print("=" * 50)
        print("Testing with prompt: 'create a login form with blue buttons'")
        print()

        result = await system.process_request("create a login form with blue buttons")

        if result.get('success'):
            logger.info("✅ Test request completed successfully")
            print("🎉 SUCCESS! The system is working correctly.")
            print(f"📊 Result: {result.get('message', 'Request processed')}")
            print(f"💡 Check your Figma file (channel: {system.channel_id}) for the results!")
        else:
            logger.error("❌ Test request failed")
            print("❌ Test request failed:")
            print(f"   Error: {result.get('error', 'Unknown error')}")

        print("\n" + "=" * 50)
        print("Test completed. System will now shutdown.")
        print("To run interactively, modify main.py to enable interactive mode.")

    except KeyboardInterrupt:
        logger.info("👋 Received interrupt signal")
        print("\n👋 Shutting down...")
    except Exception as e:
        logger.error(f"System error: {e}")
        print(f"❌ System error: {e}")
        
    finally:
        await system.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
