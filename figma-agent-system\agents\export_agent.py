"""
Export Agent for Figma Agent System.
Handles file exports, format conversions, and asset delivery.
"""

import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentRequest, AgentResponse, AgentStatus, AgentCapability
from config.agent_activities import FIGMA_ACTIVITIES
from tools.mcp_integration import MC<PERSON>lient, ToolRegistry, MCPTool
from tools.figma_api import FigmaAPIClient
from llm.gemini_client import GeminiClient
from utils.logging_config import create_agent_logger


class ExportAgent(BaseAgent):
    """
    Export Agent specializing in file exports and asset delivery.
    
    Capabilities:
    - File format exports (PNG, JPG, SVG, PDF)
    - Asset optimization and compression
    - Batch export operations
    - Export settings management
    - Delivery and distribution
    """
    
    def __init__(
        self,
        mcp_client: MCPClient = None,
        figma_client: FigmaAPIClient = None,
        gemini_client: GeminiClient = None
    ):
        super().__init__(
            name="export_agent",
            capabilities=[AgentCapability.EXPORT]
        )
        
        # Initialize clients
        self.mcp_client = mcp_client or MCPClient()
        self.figma_client = figma_client or FigmaAPIClient()
        self.gemini_client = gemini_client or GeminiClient()
        
        # Initialize tool registry
        self.tool_registry = ToolRegistry(self.mcp_client)
        self._register_export_tools()
        
        # Agent-specific logger
        self.logger = create_agent_logger("export")
        
        # Export-specific metrics
        self.files_exported = 0
        self.assets_optimized = 0
        self.batch_exports = 0
        
        self.logger.info("Export Agent initialized")

    def get_supported_actions(self) -> list:
        """Get list of actions this agent can handle."""
        return [
            "export_png", "export_svg", "export_pdf", "export_jpg",
            "export_assets", "generate_code", "export_specs",
            "create_style_guide", "export_tokens", "batch_export",
            "export_prototype", "generate_handoff"
        ]

    def _can_handle_action(self, action: str) -> bool:
        """Check if this agent can handle the given action."""
        return action in self.get_supported_actions()

    def _register_export_tools(self) -> None:
        """Register export-related tools."""
        
        # Export file tool
        export_file_tool = MCPTool(
            name="export_file",
            description="Export a file or node in specified format",
            parameters={
                "node_id": {"type": str, "required": True, "description": "Node ID to export"},
                "format": {"type": str, "required": False, "default": "PNG", "description": "Export format"},
                "scale": {"type": float, "required": False, "default": 1.0, "description": "Export scale"},
                "settings": {"type": dict, "required": False, "description": "Export settings"}
            },
            handler=self._handle_export_file,
            timeout=30.0,
            max_retries=2
        )
        self.tool_registry.register_tool(export_file_tool, "export_management")
    
    async def execute_task(self, request: AgentRequest) -> AgentResponse:
        """Execute an export task."""
        self.logger.info(
            "Executing export task",
            task_id=request.task_id,
            action=request.action
        )
        
        try:
            if not self._validate_request(request):
                return AgentResponse(
                    task_id=request.task_id,
                    agent_id=self.agent_id,
                    status=AgentStatus.FAILED,
                    error="Invalid request parameters"
                )
            
            if request.action not in FIGMA_ACTIVITIES:
                return AgentResponse(
                    task_id=request.task_id,
                    agent_id=self.agent_id,
                    status=AgentStatus.FAILED,
                    error=f"Unsupported action: {request.action}"
                )
            
            activity = FIGMA_ACTIVITIES[request.action]
            
            if activity.agent_capability not in self.capabilities:
                return AgentResponse(
                    task_id=request.task_id,
                    agent_id=self.agent_id,
                    status=AgentStatus.FAILED,
                    error=f"Action '{request.action}' not supported by Export Agent"
                )
            
            result = await self._execute_action(request.action, request.parameters, request.context)
            self._update_task_metrics(request.action)
            
            return AgentResponse(
                task_id=request.task_id,
                agent_id=self.agent_id,
                status=AgentStatus.COMPLETED,
                result=result,
                execution_time=result.get("execution_time", 0.0),
                metadata={
                    "action": request.action,
                    "tools_used": result.get("tools_used", []),
                    "ai_assistance": result.get("ai_assistance", False)
                }
            )
            
        except Exception as e:
            self.logger.error(
                "Export task execution failed",
                task_id=request.task_id,
                action=request.action,
                error=str(e)
            )
            
            return AgentResponse(
                task_id=request.task_id,
                agent_id=self.agent_id,
                status=AgentStatus.FAILED,
                error=str(e)
            )
    
    async def _execute_action(
        self,
        action: str,
        parameters: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute a specific export action."""
        
        if action == "export_assets":
            response = await self.tool_registry.execute_tool("export_file", parameters, context)
            
            if response.status == "success":
                return {
                    "success": True,
                    "result": response.result,
                    "execution_time": response.execution_time,
                    "tools_used": ["export_file"]
                }
            else:
                raise Exception(f"Tool execution failed: {response.error}")
        
        else:
            raise Exception(f"Unknown action: {action}")
    
    async def _handle_export_file(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle file export."""
        node_id = parameters.get("node_id")
        if not node_id:
            raise ValueError("node_id is required")
        
        export_format = parameters.get("format", "PNG")
        scale = parameters.get("scale", 1.0)
        settings = parameters.get("settings", {})
        
        export_data = {
            "node_id": node_id,
            "format": export_format,
            "scale": scale,
            "settings": settings,
            "export_url": f"https://figma.com/export/{node_id}.{export_format.lower()}",
            "exported_at": datetime.utcnow().isoformat()
        }
        
        export_id = f"export_{int(datetime.utcnow().timestamp())}"
        self.files_exported += 1
        
        return {
            "export_id": export_id,
            "export_data": export_data,
            "success": True
        }
    
    def _validate_request(self, request: AgentRequest) -> bool:
        """Validate export request."""
        if not request.task_id or not request.action:
            return False
        
        if request.action == "export_assets":
            params = request.parameters
            if not params.get("node_id"):
                return False
        
        return True
    
    def _update_task_metrics(self, action: str) -> None:
        """Update task-specific metrics."""
        if action == "export_assets":
            self.files_exported += 1
    
    def get_agent_metrics(self) -> Dict[str, Any]:
        """Get agent-specific metrics."""
        base_metrics = super().get_agent_metrics()
        
        export_metrics = {
            "files_exported": self.files_exported,
            "assets_optimized": self.assets_optimized,
            "batch_exports": self.batch_exports
        }
        
        return {**base_metrics, **export_metrics}
    
    async def close(self) -> None:
        """Close agent and cleanup resources."""
        await self.tool_registry.close()
        await self.figma_client.close()
        self.logger.info("Export Agent closed")
