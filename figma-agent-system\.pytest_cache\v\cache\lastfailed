{"tests/test_agent_integration.py": true, "tests/test_base_agent.py::TestBaseAgent::test_agent_close": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_system_status": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_list_agents": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_get_agent_details": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_execute_task_success": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_execute_task_failure": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_execute_task_invalid_request": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_batch_execute_success": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_get_task_status_not_found": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_list_figma_activities": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_get_figma_activity_details": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_cors_headers": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_request_validation": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_system_metrics": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_design_agent_execute": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_component_agent_execute": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_text_agent_execute": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_router_process_success": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_router_process_failure": true, "tests/test_api_endpoints.py::TestAPIEndpoints::test_task_status_endpoint_exists": true}