#!/usr/bin/env python3
"""
Startup script for Figma Multi-Agent System FastAPI Server.
This script properly configures the Python path and starts the server.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path for proper imports
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Set environment variables if not already set
if not os.getenv("GEMINI_API_KEY"):
    os.environ["GEMINI_API_KEY"] = "test-key-for-development"
if not os.getenv("FIGMA_ACCESS_TOKEN"):
    os.environ["FIGMA_ACCESS_TOKEN"] = "test-token-for-development"

# Import and run the FastAPI application
import uvicorn
from api.fastapi_server import app
from config.settings import settings

def main():
    """Start the FastAPI server with proper configuration."""
    print("🚀 Starting Figma Multi-Agent System...")
    print(f"📡 Server will be available at: http://{settings.api.host}:{settings.api.port}")
    print(f"📚 API Documentation: http://{settings.api.host}:{settings.api.port}/docs")
    print(f"🔧 Debug Mode: {settings.api.debug}")
    print("=" * 60)
    
    try:
        # Use app instance directly (no reload for stability)
        print(f"🔧 Starting server on {settings.api.host}:{settings.api.port}")
        uvicorn.run(
            app,
            host=settings.api.host,
            port=settings.api.port,
            log_level="debug",
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Server shutdown requested by user")
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
