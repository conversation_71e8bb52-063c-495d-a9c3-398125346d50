#!/usr/bin/env python3
"""
Startup script for Figma Multi-Agent System FastAPI Server.
This script properly configures the Python path and starts the server.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path for proper imports
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Environment variables are loaded from .env file via settings.py
# The load_dotenv() call in settings.py handles loading from .env file

# Import and run the FastAPI application
import uvicorn
from api.fastapi_server import app
from config.settings import settings

def main():
    """Start the FastAPI server with proper configuration."""
    print("🚀 Starting Figma Multi-Agent System...")
    print(f"📡 Server will be available at: http://{settings.api.host}:{settings.api.port}")
    print(f"📚 API Documentation: http://{settings.api.host}:{settings.api.port}/docs")
    print(f"🔧 Debug Mode: {settings.api.debug}")

    # Validate API keys are loaded from .env file
    print("\n🔑 Validating API Configuration:")
    gemini_key = settings.gemini.api_key
    figma_token = settings.figma.access_token

    if gemini_key and gemini_key != "your_gemini_api_key_here":
        print(f"✅ Gemini API Key: {gemini_key[:10]}...{gemini_key[-4:]} (loaded from .env)")
    else:
        print("❌ Gemini API Key: Not configured or using placeholder")

    if figma_token and figma_token != "your_figma_access_token_here":
        print(f"✅ Figma Access Token: {figma_token[:10]}...{figma_token[-4:]} (loaded from .env)")
    else:
        print("❌ Figma Access Token: Not configured or using placeholder")

    print("=" * 60)
    
    try:
        # Use app instance directly (no reload for stability)
        print(f"🔧 Starting server on {settings.api.host}:{settings.api.port}")
        uvicorn.run(
            app,
            host=settings.api.host,
            port=settings.api.port,
            log_level="debug",
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Server shutdown requested by user")
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
