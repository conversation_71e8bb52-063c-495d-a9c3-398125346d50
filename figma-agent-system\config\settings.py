"""
Configuration management for Figma Agent System.
Handles environment variables and application settings.
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class GeminiSettings(BaseSettings):
    """Google Gemini API configuration."""
    
    api_key: str = Field(..., env="GEMINI_API_KEY")
    model: str = Field(default="gemini-1.5-pro", env="GEMINI_MODEL")
    temperature: float = Field(default=0.7, env="GEMINI_TEMPERATURE")
    max_tokens: int = Field(default=4096, env="GEMINI_MAX_TOKENS")
    
    class Config:
        env_prefix = "GEMINI_"


class FigmaSettings(BaseSettings):
    """Figma API configuration."""
    
    access_token: str = Field(..., env="FIGMA_ACCESS_TOKEN")
    api_base_url: str = Field(default="https://api.figma.com/v1", env="FIGMA_API_BASE_URL")
    timeout: int = Field(default=30, env="FIGMA_TIMEOUT")
    
    class Config:
        env_prefix = "FIGMA_"


class MCPSettings(BaseSettings):
    """MCP (Model Context Protocol) configuration."""
    
    server_url: str = Field(default="http://localhost:3000", env="MCP_SERVER_URL")
    timeout: int = Field(default=30, env="MCP_TIMEOUT")
    
    class Config:
        env_prefix = "MCP_"


class APISettings(BaseSettings):
    """FastAPI server configuration."""

    host: str = Field(default="0.0.0.0", env="API_HOST")
    port: int = Field(default=8001, env="API_PORT")
    debug: bool = Field(default=True, env="API_DEBUG")
    reload: bool = Field(default=True, env="API_RELOAD")
    cors_origins: list = Field(default=["*"], env="API_CORS_ORIGINS")

    class Config:
        env_prefix = "API_"


class SecuritySettings(BaseSettings):
    """Security and authentication configuration."""
    
    secret_key: str = Field("dev-secret-key-change-in-production", env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    class Config:
        env_prefix = "SECURITY_"


class AgentSettings(BaseSettings):
    """Agent system configuration."""
    
    max_concurrent_agents: int = Field(default=5, env="MAX_CONCURRENT_AGENTS")
    agent_timeout: int = Field(default=60, env="AGENT_TIMEOUT")
    retry_attempts: int = Field(default=3, env="RETRY_ATTEMPTS")
    retry_delay: float = Field(default=1.0, env="RETRY_DELAY")
    
    class Config:
        env_prefix = "AGENT_"


class CacheSettings(BaseSettings):
    """Cache configuration."""
    
    ttl: int = Field(default=300, env="CACHE_TTL")
    max_size: int = Field(default=1000, env="CACHE_MAX_SIZE")
    
    class Config:
        env_prefix = "CACHE_"


class LoggingSettings(BaseSettings):
    """Logging configuration."""
    
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    
    class Config:
        env_prefix = "LOG_"


class Settings:
    """Main settings class that aggregates all configuration."""
    
    def __init__(self):
        self.gemini = GeminiSettings()
        self.figma = FigmaSettings()
        self.mcp = MCPSettings()
        self.api = APISettings()
        self.security = SecuritySettings()
        self.agent = AgentSettings()
        self.cache = CacheSettings()
        self.logging = LoggingSettings()
    
    def validate_required_settings(self) -> bool:
        """Validate that all required settings are present."""
        required_settings = [
            (self.gemini.api_key, "GEMINI_API_KEY"),
            (self.figma.access_token, "FIGMA_ACCESS_TOKEN"),
            (self.security.secret_key, "SECRET_KEY"),
        ]
        
        missing_settings = []
        for value, name in required_settings:
            if not value or value == f"your_{name.lower()}_here":
                missing_settings.append(name)
        
        if missing_settings:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_settings)}")
        
        return True


# Global settings instance
settings = Settings()
