"""
Additional agent endpoints for the FastAPI server.
Contains endpoints for Color, Image, Prototype, Export, and Collaboration agents.
"""

import time
from fastapi import HTTPException, Depends, status

from .models import AgentRequestModel, AgentResponseModel
from agents.base_agent import AgentRe<PERSON>


def create_color_agent_endpoint(app, api_instance, track_request_time):
    """Create Color Agent endpoint."""
    
    @app.post("/agents/color/execute", response_model=AgentResponseModel, tags=["Agents"])
    async def execute_color_task(
        request: AgentRequestModel,
        start_time: float = Depends(track_request_time)
    ):
        """Execute a task directly on the Color Agent."""
        try:
            api_instance.logger.info(
                "Executing color task",
                task_id=request.task_id,
                action=request.action
            )
            
            api_instance.active_tasks += 1
            
            agent_request = AgentRequest(
                task_id=request.task_id,
                action=request.action,
                parameters=request.parameters,
                context=request.context,
                priority=request.priority,
                timeout=request.timeout
            )

            response = await api_instance.color_agent.execute_task(agent_request)
            api_instance.active_tasks -= 1
            
            execution_time = time.time() - start_time
            success = response.status == "completed"
            api_instance.update_metrics(success, execution_time)
            
            return AgentResponseModel(
                task_id=response.task_id,
                agent_id=response.agent_name,  # Use agent_name as agent_id
                status=response.status.value,
                result=response.result,
                error=response.error,
                execution_time=response.execution_time,
                metadata=response.metadata
            )
            
        except Exception as e:
            api_instance.active_tasks = max(0, api_instance.active_tasks - 1)
            execution_time = time.time() - start_time
            api_instance.update_metrics(False, execution_time)
            
            api_instance.logger.error(
                "Color task execution failed",
                task_id=request.task_id,
                error=str(e)
            )
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Task execution failed: {str(e)}"
            )


def create_image_agent_endpoint(app, api_instance, track_request_time):
    """Create Image Agent endpoint."""
    
    @app.post("/agents/image/execute", response_model=AgentResponseModel, tags=["Agents"])
    async def execute_image_task(
        request: AgentRequestModel,
        start_time: float = Depends(track_request_time)
    ):
        """Execute a task directly on the Image Agent."""
        try:
            api_instance.logger.info(
                "Executing image task",
                task_id=request.task_id,
                action=request.action
            )
            
            api_instance.active_tasks += 1
            
            agent_request = AgentRequest(
                task_id=request.task_id,
                action=request.action,
                parameters=request.parameters,
                context=request.context,
                priority=request.priority,
                timeout=request.timeout
            )

            response = await api_instance.image_agent.execute_task(agent_request)
            api_instance.active_tasks -= 1
            
            execution_time = time.time() - start_time
            success = response.status == "completed"
            api_instance.update_metrics(success, execution_time)
            
            return AgentResponseModel(
                task_id=response.task_id,
                agent_id=response.agent_name,  # Use agent_name as agent_id
                status=response.status.value,
                result=response.result,
                error=response.error,
                execution_time=response.execution_time,
                metadata=response.metadata
            )
            
        except Exception as e:
            api_instance.active_tasks = max(0, api_instance.active_tasks - 1)
            execution_time = time.time() - start_time
            api_instance.update_metrics(False, execution_time)
            
            api_instance.logger.error(
                "Image task execution failed",
                task_id=request.task_id,
                error=str(e)
            )
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Task execution failed: {str(e)}"
            )


def create_prototype_agent_endpoint(app, api_instance, track_request_time):
    """Create Prototype Agent endpoint."""
    
    @app.post("/agents/prototype/execute", response_model=AgentResponseModel, tags=["Agents"])
    async def execute_prototype_task(
        request: AgentRequestModel,
        start_time: float = Depends(track_request_time)
    ):
        """Execute a task directly on the Prototype Agent."""
        try:
            api_instance.logger.info(
                "Executing prototype task",
                task_id=request.task_id,
                action=request.action
            )
            
            api_instance.active_tasks += 1
            
            agent_request = AgentRequest(
                task_id=request.task_id,
                action=request.action,
                parameters=request.parameters,
                context=request.context,
                priority=request.priority,
                timeout=request.timeout
            )

            response = await api_instance.prototype_agent.execute_task(agent_request)
            api_instance.active_tasks -= 1
            
            execution_time = time.time() - start_time
            success = response.status == "completed"
            api_instance.update_metrics(success, execution_time)
            
            return AgentResponseModel(
                task_id=response.task_id,
                agent_id=response.agent_name,  # Use agent_name as agent_id
                status=response.status.value,
                result=response.result,
                error=response.error,
                execution_time=response.execution_time,
                metadata=response.metadata
            )
            
        except Exception as e:
            api_instance.active_tasks = max(0, api_instance.active_tasks - 1)
            execution_time = time.time() - start_time
            api_instance.update_metrics(False, execution_time)
            
            api_instance.logger.error(
                "Prototype task execution failed",
                task_id=request.task_id,
                error=str(e)
            )
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Task execution failed: {str(e)}"
            )


def create_export_agent_endpoint(app, api_instance, track_request_time):
    """Create Export Agent endpoint."""
    
    @app.post("/agents/export/execute", response_model=AgentResponseModel, tags=["Agents"])
    async def execute_export_task(
        request: AgentRequestModel,
        start_time: float = Depends(track_request_time)
    ):
        """Execute a task directly on the Export Agent."""
        try:
            api_instance.logger.info(
                "Executing export task",
                task_id=request.task_id,
                action=request.action
            )
            
            api_instance.active_tasks += 1
            
            agent_request = AgentRequest(
                task_id=request.task_id,
                action=request.action,
                parameters=request.parameters,
                context=request.context,
                priority=request.priority,
                timeout=request.timeout
            )

            response = await api_instance.export_agent.execute_task(agent_request)
            api_instance.active_tasks -= 1
            
            execution_time = time.time() - start_time
            success = response.status == "completed"
            api_instance.update_metrics(success, execution_time)
            
            return AgentResponseModel(
                task_id=response.task_id,
                agent_id=response.agent_name,  # Use agent_name as agent_id
                status=response.status.value,
                result=response.result,
                error=response.error,
                execution_time=response.execution_time,
                metadata=response.metadata
            )
            
        except Exception as e:
            api_instance.active_tasks = max(0, api_instance.active_tasks - 1)
            execution_time = time.time() - start_time
            api_instance.update_metrics(False, execution_time)
            
            api_instance.logger.error(
                "Export task execution failed",
                task_id=request.task_id,
                error=str(e)
            )
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Task execution failed: {str(e)}"
            )


def create_collaboration_agent_endpoint(app, api_instance, track_request_time):
    """Create Collaboration Agent endpoint."""
    
    @app.post("/agents/collaboration/execute", response_model=AgentResponseModel, tags=["Agents"])
    async def execute_collaboration_task(
        request: AgentRequestModel,
        start_time: float = Depends(track_request_time)
    ):
        """Execute a task directly on the Collaboration Agent."""
        try:
            api_instance.logger.info(
                "Executing collaboration task",
                task_id=request.task_id,
                action=request.action
            )
            
            api_instance.active_tasks += 1
            
            agent_request = AgentRequest(
                task_id=request.task_id,
                action=request.action,
                parameters=request.parameters,
                context=request.context,
                priority=request.priority,
                timeout=request.timeout
            )

            response = await api_instance.collaboration_agent.execute_task(agent_request)
            api_instance.active_tasks -= 1
            
            execution_time = time.time() - start_time
            success = response.status == "completed"
            api_instance.update_metrics(False, execution_time)
            
            return AgentResponseModel(
                task_id=response.task_id,
                agent_id=response.agent_name,  # Use agent_name as agent_id
                status=response.status.value,
                result=response.result,
                error=response.error,
                execution_time=response.execution_time,
                metadata=response.metadata
            )
            
        except Exception as e:
            api_instance.active_tasks = max(0, api_instance.active_tasks - 1)
            execution_time = time.time() - start_time
            api_instance.update_metrics(False, execution_time)
            
            api_instance.logger.error(
                "Collaboration task execution failed",
                task_id=request.task_id,
                error=str(e)
            )
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Task execution failed: {str(e)}"
            )


def register_all_agent_endpoints(app, api_instance, track_request_time):
    """Register all additional agent endpoints."""
    create_color_agent_endpoint(app, api_instance, track_request_time)
    create_image_agent_endpoint(app, api_instance, track_request_time)
    create_prototype_agent_endpoint(app, api_instance, track_request_time)
    create_export_agent_endpoint(app, api_instance, track_request_time)
    create_collaboration_agent_endpoint(app, api_instance, track_request_time)
