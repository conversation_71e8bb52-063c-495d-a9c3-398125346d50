# Channel Configuration Guide for Claude Talk to Figma Integration

## 🎯 Your Channel Configuration: **oa34ym6m**

Based on your configuration, your Claude Talk to Figma plugin is actively using channel **`oa34ym6m`**. This is the channel ID that all your agents should use for communication.

---

## 1. 📡 How to Specify Which Channel Agents Should Join

### **Method 1: Default Channel Configuration (Recommended)**
All agents automatically use your active channel:

```python
from tools.figma_mcp_tools import get_claude_talk_to_figma_client

# All agents will use your active channel by default
client = get_claude_talk_to_figma_client(channel_id="oa34ym6m")
```

### **Method 2: Context-Based Channel Selection**
Specify channel in individual requests:

```python
# In agent execution context
context = {
    "agent_name": "design_agent",
    "channel_id": "1re91tfd",  # Your active channel
    "task_id": "unique-task-id"
}

response = await client.execute_tool("get_figma_data", parameters, context)
```

### **Method 3: API Request Channel Specification**
Include channel in FastAPI requests:

```bash
curl -X POST "http://localhost:8000/agents/design/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "task_description": "Get Figma file information",
    "parameters": {"fileKey": "your-file-key"},
    "context": {
      "channel_id": "1re91tfd"
    }
  }'
```

---

## 2. ⚙️ Configuration Options for Channel ID

### **A. Environment-Based Configuration**
Create a `.env` file in your project root:

```env
# .env file
FIGMA_CHANNEL_ID=1re91tfd
FIGMA_ACCESS_TOKEN=your-figma-token
GEMINI_API_KEY=your-gemini-key
```

### **B. Configuration File**
Use the provided channel configuration:

```python
# config/channel_config.py
from config.channel_config import get_channel_config

# Single channel configuration (recommended)
config = get_channel_config("single")  # Uses "1re91tfd" by default
```

### **C. Direct Configuration**
Set channel directly in your code:

```python
from tools.figma_mcp_tools import FigmaMCPConfig, FigmaMCPClient

config = FigmaMCPConfig(
    server_url="http://localhost:3055",
    default_channel="1re91tfd",  # Your active channel
    auto_join_channel=True,
    channel_timeout=10.0
)

client = FigmaMCPClient(config)
```

---

## 3. 🔄 Channel Joining Process in Agent Execution Flow

### **Step-by-Step Flow:**

1. **Agent Receives Task**
   ```
   User Request → FastAPI → Task Router → Specific Agent
   ```

2. **Channel Context Extraction**
   ```python
   # Agent extracts channel from context
   channel_id = context.get("channel_id", "1re91tfd")  # Your default
   ```

3. **WebSocket Connection**
   ```python
   # Connect to your MCP server
   ws_url = "ws://localhost:3055"
   async with websockets.connect(ws_url) as websocket:
   ```

4. **System Message Reception**
   ```json
   {"type": "system", "message": "Please join a channel to start communicating with Figma"}
   ```

5. **Channel Joining**
   ```python
   join_message = {"type": "join", "channel": "1re91tfd"}
   await websocket.send(json.dumps(join_message))
   ```

6. **Join Confirmation**
   ```json
   {"type": "system", "message": "Joined channel: 1re91tfd", "channel": "1re91tfd"}
   ```

7. **Tool Execution**
   ```python
   tool_request = {
       "type": "tool_call",
       "tool": "get_figma_data",
       "parameters": {"fileKey": "your-file"},
       "id": "unique-request-id"
   }
   ```

8. **Response Processing**
   ```
   MCP Server → Figma Plugin → Figma API → Results → Agent → User
   ```

---

## 4. 🤖 Configuring Different Channels for Different Agents

### **Agent-Specific Channel Configuration:**

```python
from tools.figma_mcp_tools import get_claude_talk_to_figma_client

# Configure different channels per agent (if needed)
client = get_claude_talk_to_figma_client(
    channel_id="1re91tfd",  # Default channel
    channel_per_agent={
        "design_agent": "1re91tfd",      # Your active channel
        "color_agent": "1re91tfd",       # Same channel for consistency
        "text_agent": "1re91tfd",        # Same channel
        "component_agent": "1re91tfd",   # Same channel
        "image_agent": "1re91tfd",       # Same channel
        "prototype_agent": "1re91tfd",   # Same channel
        "export_agent": "1re91tfd",      # Same channel
        "collaboration_agent": "1re91tfd" # Same channel
    }
)
```

### **Why Use the Same Channel?**
✅ **Recommended**: Use `1re91tfd` for all agents because:
- It's your active Figma plugin channel
- Ensures consistent communication
- Simplifies troubleshooting
- Matches your current setup

❌ **Not Recommended**: Using different channels unless you have multiple Figma plugin instances

---

## 5. 🛠️ Troubleshooting Channel Connection Issues

### **A. Connection Diagnostics**

```bash
# Test channel connectivity
cd figma-agent-system
python test_channel_configuration.py
```

### **B. Common Issues and Solutions**

#### **Issue 1: Channel Not Found**
```
Error: Channel '1re91tfd' not found or not accessible
```
**Solutions:**
- ✅ Ensure Figma is open
- ✅ Verify your Claude Talk to Figma plugin is active
- ✅ Check that channel `1re91tfd` is connected
- ✅ Restart your MCP server if needed

#### **Issue 2: WebSocket Connection Failed**
```
Error: Cannot connect to ws://localhost:3055
```
**Solutions:**
- ✅ Verify your MCP server is running: `http://localhost:3055/status`
- ✅ Check port 3055 is not blocked by firewall
- ✅ Ensure no other service is using port 3055

#### **Issue 3: Channel Join Timeout**
```
Error: Channel join timeout after 10 seconds
```
**Solutions:**
- ✅ Increase channel timeout in configuration
- ✅ Check Figma plugin responsiveness
- ✅ Verify network connectivity

#### **Issue 4: Tool Execution Fails**
```
Error: Tool execution failed on channel 1re91tfd
```
**Solutions:**
- ✅ Verify Figma file permissions
- ✅ Check that you have access to the specified file
- ✅ Ensure the Figma plugin has necessary permissions
- ✅ Monitor MCP server logs for detailed errors

### **C. Debug Commands**

```bash
# Check MCP server status
curl http://localhost:3055/status

# Test WebSocket connection
python test_websocket_communication.py

# Test channel configuration
python test_channel_configuration.py

# Run channel usage examples
python examples/channel_usage_examples.py
```

### **D. Monitoring and Logging**

```python
# Enable detailed logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Monitor channel activity
from tools.figma_mcp_tools import get_claude_talk_to_figma_client

client = get_claude_talk_to_figma_client(channel_id="1re91tfd")

# Test connection with detailed output
result = await client.test_channel_connection("1re91tfd")
print(f"Connection result: {result}")
```

---

## 🎉 Quick Start Checklist

### **✅ Pre-flight Checklist:**
- [ ] Claude Talk to Figma MCP server running on port 3055
- [ ] Figma is open with your plugin active
- [ ] Channel `1re91tfd` is connected and accessible
- [ ] Figma Agent System is configured with channel settings

### **✅ Configuration Checklist:**
- [ ] Default channel set to `1re91tfd`
- [ ] All agents configured to use your active channel
- [ ] Channel timeout set appropriately (10-15 seconds)
- [ ] Auto-join channel enabled

### **✅ Testing Checklist:**
- [ ] Channel connectivity test passes
- [ ] Agent channel routing works correctly
- [ ] Tool execution succeeds with channel context
- [ ] Error handling works for invalid channels

---

## 🚀 Ready to Use!

Your channel configuration is now complete! All agents will automatically use your active Figma plugin channel `1re91tfd` for communication with your Claude Talk to Figma MCP server.

### **Next Steps:**
1. **Start your servers**: MCP server (port 3055) and Figma Agent System (port 8000)
2. **Test the integration**: Use the provided test scripts
3. **Make API requests**: Include channel context in your requests
4. **Monitor performance**: Watch for channel connectivity and tool execution success

Your Figma Agent System is now properly configured for channel communication! 🎯
