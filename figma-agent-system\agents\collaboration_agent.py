"""
Collaboration Agent for Figma Agent System.
Handles team collaboration, comments, and sharing operations.
"""

import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentRequest, AgentResponse, AgentStatus, AgentCapability
from config.agent_activities import FIGMA_ACTIVITIES
from tools.mcp_integration import MC<PERSON><PERSON>, ToolRegistry, MCPTool
from tools.figma_api import FigmaAPIClient
from llm.gemini_client import GeminiClient
from utils.logging_config import create_agent_logger
from utils.validation import sanitize_string


class CollaborationAgent(BaseAgent):
    """
    Collaboration Agent specializing in team features and communication.
    
    Capabilities:
    - Comment management and threading
    - Team sharing and permissions
    - Version control and history
    - Notification and communication
    - Workflow automation
    """
    
    def __init__(
        self,
        mcp_client: MCPClient = None,
        figma_client: FigmaAPIClient = None,
        gemini_client: GeminiClient = None
    ):
        super().__init__(
            name="collaboration_agent",
            capabilities=[AgentCapability.COLLABORATION]
        )
        
        # Initialize clients
        self.mcp_client = mcp_client or MCPClient()
        self.figma_client = figma_client or FigmaAPIClient()
        self.gemini_client = gemini_client or GeminiClient()
        
        # Initialize tool registry
        self.tool_registry = ToolRegistry(self.mcp_client)
        self._register_collaboration_tools()
        
        # Agent-specific logger
        self.logger = create_agent_logger("collaboration")
        
        # Collaboration-specific metrics
        self.comments_created = 0
        self.shares_managed = 0
        self.notifications_sent = 0
        
        self.logger.info("Collaboration Agent initialized")

    def get_supported_actions(self) -> list:
        """Get list of actions this agent can handle."""
        return [
            "add_comment", "resolve_comment", "share_file", "set_permissions",
            "create_team", "invite_user", "create_branch", "merge_branch",
            "publish_library", "create_version", "track_changes", "sync_changes"
        ]

    def _can_handle_action(self, action: str) -> bool:
        """Check if this agent can handle the given action."""
        return action in self.get_supported_actions()

    def _register_collaboration_tools(self) -> None:
        """Register collaboration-related tools."""
        
        # Create comment tool
        create_comment_tool = MCPTool(
            name="create_comment",
            description="Create a comment on a design element",
            parameters={
                "node_id": {"type": str, "required": True, "description": "Node ID to comment on"},
                "message": {"type": str, "required": True, "description": "Comment message"},
                "x": {"type": float, "required": False, "description": "Comment X position"},
                "y": {"type": float, "required": False, "description": "Comment Y position"},
                "mentions": {"type": list, "required": False, "description": "User mentions"}
            },
            handler=self._handle_create_comment,
            timeout=10.0,
            max_retries=2
        )
        self.tool_registry.register_tool(create_comment_tool, "collaboration_management")
        
        # Share file tool
        share_file_tool = MCPTool(
            name="share_file",
            description="Share a file with team members",
            parameters={
                "file_id": {"type": str, "required": True, "description": "File ID to share"},
                "users": {"type": list, "required": True, "description": "List of users to share with"},
                "permission": {"type": str, "required": False, "default": "view", "description": "Permission level"},
                "message": {"type": str, "required": False, "description": "Share message"}
            },
            handler=self._handle_share_file,
            timeout=15.0,
            max_retries=2
        )
        self.tool_registry.register_tool(share_file_tool, "collaboration_management")
    
    async def execute_task(self, request: AgentRequest) -> AgentResponse:
        """Execute a collaboration task."""
        self.logger.info(
            "Executing collaboration task",
            task_id=request.task_id,
            action=request.action
        )
        
        try:
            if not self._validate_request(request):
                return AgentResponse(
                    task_id=request.task_id,
                    agent_id=self.agent_id,
                    status=AgentStatus.FAILED,
                    error="Invalid request parameters"
                )
            
            if request.action not in FIGMA_ACTIVITIES:
                return AgentResponse(
                    task_id=request.task_id,
                    agent_id=self.agent_id,
                    status=AgentStatus.FAILED,
                    error=f"Unsupported action: {request.action}"
                )
            
            activity = FIGMA_ACTIVITIES[request.action]
            
            if activity.agent_capability not in self.capabilities:
                return AgentResponse(
                    task_id=request.task_id,
                    agent_id=self.agent_id,
                    status=AgentStatus.FAILED,
                    error=f"Action '{request.action}' not supported by Collaboration Agent"
                )
            
            result = await self._execute_action(request.action, request.parameters, request.context)
            self._update_task_metrics(request.action)
            
            return AgentResponse(
                task_id=request.task_id,
                agent_id=self.agent_id,
                status=AgentStatus.COMPLETED,
                result=result,
                execution_time=result.get("execution_time", 0.0),
                metadata={
                    "action": request.action,
                    "tools_used": result.get("tools_used", []),
                    "ai_assistance": result.get("ai_assistance", False)
                }
            )
            
        except Exception as e:
            self.logger.error(
                "Collaboration task execution failed",
                task_id=request.task_id,
                action=request.action,
                error=str(e)
            )
            
            return AgentResponse(
                task_id=request.task_id,
                agent_id=self.agent_id,
                status=AgentStatus.FAILED,
                error=str(e)
            )
    
    async def _execute_action(
        self,
        action: str,
        parameters: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute a specific collaboration action."""
        
        action_tool_map = {
            "add_comment": "create_comment",
            "share_file": "share_file"
        }
        
        if action in action_tool_map:
            tool_name = action_tool_map[action]
            response = await self.tool_registry.execute_tool(tool_name, parameters, context)
            
            if response.status == "success":
                return {
                    "success": True,
                    "result": response.result,
                    "execution_time": response.execution_time,
                    "tools_used": [tool_name]
                }
            else:
                raise Exception(f"Tool execution failed: {response.error}")
        
        else:
            raise Exception(f"Unknown action: {action}")
    
    async def _handle_create_comment(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle comment creation."""
        node_id = parameters.get("node_id")
        message = parameters.get("message")
        
        if not node_id or not message:
            raise ValueError("node_id and message are required")
        
        # Sanitize message
        message = sanitize_string(message)
        
        comment_data = {
            "node_id": node_id,
            "message": message,
            "x": parameters.get("x", 0),
            "y": parameters.get("y", 0),
            "mentions": parameters.get("mentions", []),
            "author": "system",  # Would be actual user in real implementation
            "created_at": datetime.utcnow().isoformat(),
            "resolved": False
        }
        
        comment_id = f"comment_{int(datetime.utcnow().timestamp())}"
        self.comments_created += 1
        
        return {
            "comment_id": comment_id,
            "comment_data": comment_data,
            "success": True
        }
    
    async def _handle_share_file(self, parameters: Dict, context: Dict, client) -> Dict[str, Any]:
        """Handle file sharing."""
        file_id = parameters.get("file_id")
        users = parameters.get("users")
        
        if not file_id or not users:
            raise ValueError("file_id and users are required")
        
        permission = parameters.get("permission", "view")
        message = parameters.get("message", "")
        
        share_data = {
            "file_id": file_id,
            "users": users,
            "permission": permission,
            "message": sanitize_string(message) if message else "",
            "shared_by": "system",  # Would be actual user in real implementation
            "shared_at": datetime.utcnow().isoformat()
        }
        
        share_id = f"share_{int(datetime.utcnow().timestamp())}"
        self.shares_managed += 1
        
        return {
            "share_id": share_id,
            "share_data": share_data,
            "success": True
        }
    
    def _validate_request(self, request: AgentRequest) -> bool:
        """Validate collaboration request."""
        if not request.task_id or not request.action:
            return False
        
        if request.action == "add_comment":
            params = request.parameters
            if not params.get("node_id") or not params.get("message"):
                return False
        elif request.action == "share_file":
            params = request.parameters
            if not params.get("file_id") or not params.get("users"):
                return False
        
        return True
    
    def _update_task_metrics(self, action: str) -> None:
        """Update task-specific metrics."""
        if action == "add_comment":
            self.comments_created += 1
        elif action == "share_file":
            self.shares_managed += 1
    
    def get_agent_metrics(self) -> Dict[str, Any]:
        """Get agent-specific metrics."""
        base_metrics = super().get_agent_metrics()
        
        collaboration_metrics = {
            "comments_created": self.comments_created,
            "shares_managed": self.shares_managed,
            "notifications_sent": self.notifications_sent
        }
        
        return {**base_metrics, **collaboration_metrics}
    
    async def close(self) -> None:
        """Close agent and cleanup resources."""
        await self.tool_registry.close()
        await self.figma_client.close()
        self.logger.info("Collaboration Agent closed")
