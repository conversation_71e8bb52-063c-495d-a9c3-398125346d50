#!/usr/bin/env python3
"""
Test Figma API Commands - Focus on the working format

Based on discovery, the "figma_api" command type got a broadcast response.
Let's test this format more thoroughly.
"""

import asyncio
import json
import websockets
from datetime import datetime

# Configuration
WEBSOCKET_URL = "ws://localhost:3055"
CHANNEL_ID = "oa34ym6m"

async def test_figma_api_commands():
    """Test the figma_api command format that showed promise."""
    print(f"🎯 Testing Figma API Commands")
    print(f"Channel: {CHANNEL_ID}")
    print("=" * 70)
    
    try:
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            print(f"✅ Connected to {WEBSOCKET_URL}")
            
            # Join channel
            join_message = {"type": "join", "channel": CHANNEL_ID}
            await websocket.send(json.dumps(join_message))
            join_response = await websocket.recv()
            print(f"✅ Joined: {join_response}")
            
            # Test various Figma API commands
            figma_commands = [
                {
                    "name": "Create Frame - Basic",
                    "command": {
                        "type": "figma_api",
                        "command": "createFrame",
                        "parameters": {
                            "width": 400,
                            "height": 300,
                            "name": "API Test Frame 1"
                        }
                    }
                },
                {
                    "name": "Create Frame - With Position",
                    "command": {
                        "type": "figma_api",
                        "command": "createFrame",
                        "parameters": {
                            "width": 500,
                            "height": 400,
                            "name": "API Test Frame 2",
                            "x": 100,
                            "y": 100
                        }
                    }
                },
                {
                    "name": "Create Rectangle",
                    "command": {
                        "type": "figma_api",
                        "command": "createRectangle",
                        "parameters": {
                            "width": 200,
                            "height": 150,
                            "name": "API Test Rectangle"
                        }
                    }
                },
                {
                    "name": "Create Text",
                    "command": {
                        "type": "figma_api",
                        "command": "createText",
                        "parameters": {
                            "text": "Hello from Agent!",
                            "name": "API Test Text"
                        }
                    }
                },
                {
                    "name": "Get Current Page",
                    "command": {
                        "type": "figma_api",
                        "command": "getCurrentPage",
                        "parameters": {}
                    }
                },
                {
                    "name": "Get Selection",
                    "command": {
                        "type": "figma_api",
                        "command": "getSelection",
                        "parameters": {}
                    }
                }
            ]
            
            successful_commands = []
            
            for i, cmd_info in enumerate(figma_commands):
                print(f"\n🧪 Test {i+1}: {cmd_info['name']}")
                
                try:
                    # Send command
                    await websocket.send(json.dumps(cmd_info['command']))
                    print(f"   📤 Sent: {json.dumps(cmd_info['command'], indent=6)}")
                    
                    # Wait for response
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=8.0)
                        response_data = json.loads(response)
                        print(f"   ✅ Response: {json.dumps(response_data, indent=6)}")
                        
                        # Check if this is a broadcast (indicates command was processed)
                        if response_data.get("type") == "broadcast":
                            successful_commands.append(cmd_info['name'])
                            print(f"   🎉 SUCCESS: Command was broadcast!")
                        
                    except asyncio.TimeoutError:
                        print(f"   ⏰ No response (timeout)")
                    
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                
                # Wait between commands
                await asyncio.sleep(2)
            
            print(f"\n📊 FIGMA API COMMAND RESULTS")
            print("=" * 70)
            print(f"Commands tested: {len(figma_commands)}")
            print(f"Successful broadcasts: {len(successful_commands)}")
            
            if successful_commands:
                print(f"✅ Commands that were broadcast (likely executed):")
                for cmd in successful_commands:
                    print(f"   - {cmd}")
                
                print(f"\n🎉 SUCCESS! Check your Figma file for new elements!")
                print(f"💡 The 'figma_api' command type appears to work!")
            else:
                print(f"❌ No commands were successfully broadcast")
            
            return successful_commands
            
    except Exception as e:
        print(f"❌ Figma API command test failed: {e}")
        import traceback
        traceback.print_exc()
        return []

async def test_create_frame_variations():
    """Test different variations of frame creation."""
    print(f"\n🖼️  Testing Frame Creation Variations")
    print("=" * 70)
    
    try:
        async with websockets.connect(WEBSOCKET_URL) as websocket:
            # Join channel
            join_message = {"type": "join", "channel": CHANNEL_ID}
            await websocket.send(json.dumps(join_message))
            await websocket.recv()
            
            # Test different frame creation approaches
            frame_commands = [
                {
                    "name": "Simple Frame",
                    "command": {
                        "type": "figma_api",
                        "command": "createFrame",
                        "parameters": {
                            "name": "Agent Frame 1"
                        }
                    }
                },
                {
                    "name": "Sized Frame",
                    "command": {
                        "type": "figma_api",
                        "command": "createFrame",
                        "parameters": {
                            "name": "Agent Frame 2",
                            "width": 800,
                            "height": 600
                        }
                    }
                },
                {
                    "name": "Positioned Frame",
                    "command": {
                        "type": "figma_api",
                        "command": "createFrame",
                        "parameters": {
                            "name": "Agent Frame 3",
                            "width": 300,
                            "height": 200,
                            "x": 200,
                            "y": 300
                        }
                    }
                },
                {
                    "name": "Styled Frame",
                    "command": {
                        "type": "figma_api",
                        "command": "createFrame",
                        "parameters": {
                            "name": "Agent Frame 4",
                            "width": 400,
                            "height": 300,
                            "backgroundColor": "#3B82F6"
                        }
                    }
                }
            ]
            
            created_frames = []
            
            for frame_cmd in frame_commands:
                print(f"🖼️  Creating: {frame_cmd['name']}")
                
                try:
                    await websocket.send(json.dumps(frame_cmd['command']))
                    
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    
                    if response_data.get("type") == "broadcast":
                        print(f"   ✅ Frame created successfully!")
                        created_frames.append(frame_cmd['name'])
                    else:
                        print(f"   ❓ Response: {response_data}")
                    
                except asyncio.TimeoutError:
                    print(f"   ⏰ Timeout")
                except Exception as e:
                    print(f"   ❌ Error: {e}")
                
                await asyncio.sleep(1)
            
            print(f"\n📊 Frame Creation Results:")
            print(f"   Attempted: {len(frame_commands)}")
            print(f"   Successful: {len(created_frames)}")
            
            if created_frames:
                print(f"✅ Successfully created frames:")
                for frame in created_frames:
                    print(f"   - {frame}")
            
            return created_frames
            
    except Exception as e:
        print(f"❌ Frame creation test failed: {e}")
        return []

async def main():
    """Main test function."""
    print("🚀 Figma API Command Testing")
    print(f"Timestamp: {datetime.now()}")
    print("=" * 80)
    
    # Test general Figma API commands
    successful_commands = await test_figma_api_commands()
    
    # Test frame creation specifically
    created_frames = await test_create_frame_variations()
    
    # Final summary
    print(f"\n🎯 FINAL RESULTS")
    print("=" * 80)
    
    if successful_commands or created_frames:
        print(f"🎉 SUCCESS! The Figma API command format works!")
        print(f"✅ Working command format:")
        print(f"""
        {{
            "type": "figma_api",
            "command": "createFrame",
            "parameters": {{
                "width": 400,
                "height": 300,
                "name": "Frame Name"
            }}
        }}
        """)
        
        print(f"💡 Integration Steps:")
        print(f"1. Update design agent to use 'figma_api' command type")
        print(f"2. Use 'createFrame' as the command name")
        print(f"3. Pass parameters in the 'parameters' object")
        print(f"4. Look for 'broadcast' response type as success indicator")
        
        print(f"\n🔧 Next: Update the design agent implementation!")
        
    else:
        print(f"❌ No successful commands found")
        print(f"💡 The plugin might need different parameters or setup")
    
    print(f"\n📋 Check your Figma file now to see if any frames were created!")

if __name__ == "__main__":
    asyncio.run(main())
