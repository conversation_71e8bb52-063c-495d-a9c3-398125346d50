#!/usr/bin/env python3
"""
Test script to discover <PERSON> Talk to Figma MCP server endpoints and communication format.
"""

import asyncio
import httpx
import json

async def test_server_endpoints():
    """Test different endpoints to understand the MCP server API."""
    print("🔍 Discovering <PERSON> Talk to Figma MCP Server API...")
    
    server_url = "http://localhost:3055"
    
    # Test different endpoints
    endpoints_to_test = [
        "/",
        "/health",
        "/status",
        "/api",
        "/mcp",
        "/rpc",
        "/tools",
        "/tools/list",
        "/api/tools",
        "/api/tools/list",
        "/figma",
        "/figma/tools"
    ]
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        for endpoint in endpoints_to_test:
            url = f"{server_url}{endpoint}"
            
            try:
                # Test GET request
                response = await client.get(url)
                print(f"✅ GET {endpoint}: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        content = response.json()
                        print(f"   Response: {json.dumps(content, indent=2)[:200]}...")
                    except:
                        text = response.text[:200]
                        print(f"   Response: {text}...")
                
            except Exception as e:
                print(f"❌ GET {endpoint}: {str(e)[:50]}...")
            
            # Test POST request for some endpoints
            if endpoint in ["/mcp", "/rpc", "/tools", "/api/tools"]:
                try:
                    test_payload = {
                        "jsonrpc": "2.0",
                        "id": "test",
                        "method": "tools/list",
                        "params": {}
                    }
                    
                    response = await client.post(
                        url,
                        json=test_payload,
                        headers={"Content-Type": "application/json"}
                    )
                    
                    print(f"✅ POST {endpoint}: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            content = response.json()
                            print(f"   Response: {json.dumps(content, indent=2)[:200]}...")
                        except:
                            text = response.text[:200]
                            print(f"   Response: {text}...")
                            
                except Exception as e:
                    print(f"❌ POST {endpoint}: {str(e)[:50]}...")

async def test_websocket_connection():
    """Test WebSocket connection to the MCP server."""
    print("\n🔌 Testing WebSocket Connection...")
    
    import websockets
    
    ws_urls_to_test = [
        "ws://localhost:3055",
        "ws://localhost:3055/ws",
        "ws://localhost:3055/websocket",
        "ws://localhost:3055/mcp",
        "ws://localhost:3055/rpc"
    ]
    
    for ws_url in ws_urls_to_test:
        try:
            async with websockets.connect(ws_url) as websocket:
                print(f"✅ WebSocket connected: {ws_url}")
                
                # Try to send a test message
                test_message = {
                    "jsonrpc": "2.0",
                    "id": "test",
                    "method": "tools/list",
                    "params": {}
                }
                
                await websocket.send(json.dumps(test_message))
                
                # Wait for response
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    print(f"   Response: {response[:200]}...")
                except asyncio.TimeoutError:
                    print(f"   No response received (timeout)")
                
                break  # If successful, don't test other URLs
                
        except Exception as e:
            print(f"❌ WebSocket {ws_url}: {str(e)[:50]}...")

async def test_figma_plugin_communication():
    """Test communication patterns that might work with Figma plugin."""
    print("\n🎨 Testing Figma Plugin Communication Patterns...")
    
    server_url = "http://localhost:3055"
    
    # Test patterns that Figma plugins commonly use
    test_patterns = [
        {
            "name": "Direct Tool Call",
            "method": "POST",
            "endpoint": "/tools/get_figma_data",
            "payload": {
                "fileKey": "test-file-123",
                "nodeId": "test-node-456"
            }
        },
        {
            "name": "MCP JSON-RPC",
            "method": "POST", 
            "endpoint": "/mcp",
            "payload": {
                "jsonrpc": "2.0",
                "id": "test-call",
                "method": "tools/call",
                "params": {
                    "name": "get_figma_data",
                    "arguments": {
                        "fileKey": "test-file-123",
                        "nodeId": "test-node-456"
                    }
                }
            }
        },
        {
            "name": "Simple RPC",
            "method": "POST",
            "endpoint": "/rpc",
            "payload": {
                "method": "get_figma_data",
                "params": {
                    "fileKey": "test-file-123",
                    "nodeId": "test-node-456"
                }
            }
        }
    ]
    
    async with httpx.AsyncClient(timeout=15.0) as client:
        for pattern in test_patterns:
            try:
                print(f"\n🧪 Testing {pattern['name']}...")
                
                response = await client.request(
                    pattern["method"],
                    f"{server_url}{pattern['endpoint']}",
                    json=pattern["payload"],
                    headers={"Content-Type": "application/json"}
                )
                
                print(f"   Status: {response.status_code}")
                
                if response.status_code in [200, 201, 202]:
                    try:
                        content = response.json()
                        print(f"   ✅ Success: {json.dumps(content, indent=2)[:300]}...")
                    except:
                        text = response.text[:300]
                        print(f"   ✅ Success: {text}...")
                else:
                    print(f"   ❌ Failed: {response.text[:200]}...")
                    
            except Exception as e:
                print(f"   ❌ Error: {str(e)[:100]}...")

async def main():
    """Run all MCP server discovery tests."""
    print("🚀 Claude Talk to Figma MCP Server Discovery")
    print("=" * 60)
    
    await test_server_endpoints()
    await test_websocket_connection()
    await test_figma_plugin_communication()
    
    print("\n" + "=" * 60)
    print("🎯 DISCOVERY COMPLETE")
    print("\nBased on the results above, we can determine:")
    print("1. Which endpoints your MCP server supports")
    print("2. What communication format it expects")
    print("3. How to properly integrate with your tools")
    print("\nUse this information to configure the Figma Agent System integration.")

if __name__ == "__main__":
    asyncio.run(main())
