#!/usr/bin/env python3
"""
Test WebSocket communication with <PERSON> to Figma MCP server.
"""

import asyncio
import websockets
import json

async def test_websocket_communication():
    """Test WebSocket communication with <PERSON> to Figma."""
    print("🔌 Testing WebSocket Communication with <PERSON> Talk to Figma...")
    
    ws_url = "ws://localhost:3055"
    
    try:
        async with websockets.connect(ws_url) as websocket:
            print(f"✅ Connected to {ws_url}")
            
            # Receive system message
            system_message = await websocket.recv()
            system_data = json.loads(system_message)
            print(f"📨 System message: {system_data}")
            
            # Join a channel
            join_message = {
                "type": "join",
                "channel": "test-channel"
            }
            
            print(f"📤 Sending join message: {join_message}")
            await websocket.send(json.dumps(join_message))
            
            # Wait for join response
            try:
                join_response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                join_data = json.loads(join_response)
                print(f"📨 Join response: {join_data}")
                
                # Test sending a tool request
                tool_request = {
                    "type": "tool_call",
                    "tool": "get_figma_data",
                    "parameters": {
                        "fileKey": "test-file-123",
                        "nodeId": "test-node-456"
                    },
                    "id": "test-call-1"
                }
                
                print(f"📤 Sending tool request: {tool_request}")
                await websocket.send(json.dumps(tool_request))
                
                # Wait for tool response
                try:
                    tool_response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    response_data = json.loads(tool_response)
                    print(f"📨 Tool response: {response_data}")
                    
                    return True
                    
                except asyncio.TimeoutError:
                    print("⏰ Tool response timeout")
                    return False
                    
            except asyncio.TimeoutError:
                print("⏰ Join response timeout")
                return False
                
    except Exception as e:
        print(f"❌ WebSocket communication failed: {e}")
        return False

async def test_different_message_formats():
    """Test different message formats to understand the protocol."""
    print("\n🧪 Testing Different Message Formats...")
    
    ws_url = "ws://localhost:3055"
    
    message_formats = [
        {
            "name": "MCP JSON-RPC",
            "message": {
                "jsonrpc": "2.0",
                "id": "test-1",
                "method": "tools/list",
                "params": {}
            }
        },
        {
            "name": "Simple Tool Call",
            "message": {
                "type": "tool_call",
                "tool": "get_figma_data",
                "parameters": {"fileKey": "test"}
            }
        },
        {
            "name": "Direct Method Call",
            "message": {
                "method": "get_figma_data",
                "params": {"fileKey": "test"}
            }
        }
    ]
    
    try:
        async with websockets.connect(ws_url) as websocket:
            # Receive system message
            system_message = await websocket.recv()
            print(f"📨 System: {json.loads(system_message)}")
            
            # Join channel first
            await websocket.send(json.dumps({"type": "join", "channel": "test"}))
            
            try:
                join_response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                print(f"📨 Join: {json.loads(join_response)}")
            except asyncio.TimeoutError:
                print("⏰ No join response")
            
            # Test different message formats
            for format_test in message_formats:
                print(f"\n🧪 Testing {format_test['name']}...")
                
                await websocket.send(json.dumps(format_test['message']))
                
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    print(f"✅ Response: {response_data}")
                except asyncio.TimeoutError:
                    print("⏰ No response")
                except Exception as e:
                    print(f"❌ Error: {e}")
                    
    except Exception as e:
        print(f"❌ Test failed: {e}")

async def main():
    """Run WebSocket communication tests."""
    print("🚀 Claude Talk to Figma WebSocket Communication Tests")
    print("=" * 60)
    
    success = await test_websocket_communication()
    await test_different_message_formats()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 WebSocket communication is working!")
    else:
        print("❌ WebSocket communication needs debugging.")
    
    print("\n💡 Use the information above to understand:")
    print("1. What message format your MCP server expects")
    print("2. How to properly join channels")
    print("3. What responses to expect from tool calls")

if __name__ == "__main__":
    asyncio.run(main())
